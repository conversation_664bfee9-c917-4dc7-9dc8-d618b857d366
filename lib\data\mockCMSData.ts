// Mock data for CMS development and testing

import { DynamicProduct, ProductTemplate, DynamicField } from "@/lib/types"

// Mock Templates
export const mockTemplates: ProductTemplate[] = [
  {
    id: "template_simple_package",
    name: "قالب الباقات البسيط",
    description: "قالب لمنتجات الألعاب مع اختيار الباقات البسيط (مثل Free Fire, PUBG)",
    layout: "single_column",
    isActive: true,
    createdAt: new Date("2024-01-01"),
    updatedAt: new Date("2024-01-01"),
    fields: [
      {
        id: "field_image_1",
        type: "image",
        name: "product_image",
        label: "صورة المنتج",
        order: 0,
        isVisible: true,
        isRequired: false,
        validation: {}
      },
      {
        id: "field_package_1",
        type: "package_selection",
        name: "package",
        label: "اختر الباقة",
        order: 1,
        isVisible: true,
        isRequired: true,
        validation: { required: true },
        options: [
          {
            id: "pkg_1",
            label: "100 جوهرة",
            value: "100",
            price: 35,
            originalPrice: 40,
            discount: 12
          },
          {
            id: "pkg_2",
            label: "310 جوهرة",
            value: "310",
            price: 100,
            originalPrice: 120,
            discount: 17,
            popular: true
          },
          {
            id: "pkg_3",
            label: "520 جوهرة",
            value: "520",
            price: 160,
            originalPrice: 200,
            discount: 20
          }
        ]
      },
      {
        id: "field_server_1",
        type: "server_selection",
        name: "server",
        label: "اختر السيرفر",
        order: 2,
        isVisible: true,
        isRequired: true,
        validation: { required: true },
        options: [
          { id: "srv_1", label: "عالمي", value: "global" },
          { id: "srv_2", label: "كوريا", value: "korea" },
          { id: "srv_3", label: "فيتنام", value: "vietnam" }
        ]
      },
      {
        id: "field_user_id_1",
        type: "text",
        name: "user_id",
        label: "أيدي اللاعب",
        placeholder: "أدخل أيدي اللاعب",
        order: 3,
        isVisible: true,
        isRequired: true,
        validation: { required: true, minLength: 3, maxLength: 20 }
      }
    ]
  },
  {
    id: "template_complex_form",
    name: "قالب النموذج المعقد",
    description: "قالب للمنتجات التي تحتاج حقول متعددة ومعقدة (مثل TikTok)",
    layout: "single_column",
    isActive: true,
    createdAt: new Date("2024-01-02"),
    updatedAt: new Date("2024-01-02"),
    fields: [
      {
        id: "field_image_2",
        type: "image",
        name: "product_image",
        label: "صورة المنتج",
        order: 0,
        isVisible: true,
        isRequired: false,
        validation: {}
      },
      {
        id: "field_package_2",
        type: "package_selection",
        name: "coin_package",
        label: "حدد عدد العملات",
        order: 1,
        isVisible: true,
        isRequired: true,
        validation: { required: true },
        options: [
          { id: "coin_1", label: "عملة 70", value: "70", price: 25 },
          { id: "coin_2", label: "عملة 350", value: "350", price: 100 },
          { id: "coin_3", label: "عملة 700", value: "700", price: 200 },
          { id: "coin_4", label: "عملة 1400", value: "1400", price: 400 },
          { id: "coin_5", label: "عملة 3500", value: "3500", price: 1000 },
          { id: "coin_6", label: "عملة 7000", value: "7000", price: 2000 }
        ]
      },
      {
        id: "field_user_id_2",
        type: "text",
        name: "tiktok_user_id",
        label: "أيدي يوزر أو رقم الجوال أو الإيميل",
        placeholder: "أدخل أيدي المستخدم",
        order: 2,
        isVisible: true,
        isRequired: true,
        validation: { required: true, minLength: 3 }
      },
      {
        id: "field_service_type",
        type: "select",
        name: "service_type",
        label: "نوع الخدمة",
        order: 3,
        isVisible: true,
        isRequired: true,
        validation: { required: true },
        options: [
          { id: "svc_1", label: "خدمة الشحن العادية", value: "normal" },
          { id: "svc_2", label: "خدمة الشحن السريع", value: "fast" },
          { id: "svc_3", label: "خدمة الشحن الفوري", value: "instant" }
        ]
      },
      {
        id: "field_phone",
        type: "text",
        name: "phone_number",
        label: "رقم الهاتف الشخصي منك",
        placeholder: "أدخل رقم الهاتف",
        order: 4,
        isVisible: true,
        isRequired: true,
        validation: { required: true, pattern: "^[0-9+\\-\\s]+$" }
      },
      {
        id: "field_country",
        type: "select",
        name: "country",
        label: "اختر الطريقة التي تريد بها الشحن",
        order: 5,
        isVisible: true,
        isRequired: true,
        validation: { required: true },
        options: [
          { id: "method_1", label: "الشحن عبر الرقم", value: "phone" },
          { id: "method_2", label: "الشحن عبر الإيميل", value: "email" },
          { id: "method_3", label: "الشحن عبر اليوزر", value: "username" }
        ]
      }
    ]
  },
  {
    id: "template_standard_product",
    name: "قالب المنتج القياسي",
    description: "قالب للمنتجات القياسية مع معلومات إضافية",
    layout: "two_column",
    isActive: true,
    createdAt: new Date("2024-01-03"),
    updatedAt: new Date("2024-01-03"),
    fields: [
      {
        id: "field_image_3",
        type: "image",
        name: "product_image",
        label: "صورة المنتج",
        order: 0,
        isVisible: true,
        isRequired: false,
        validation: {}
      },
      {
        id: "field_offers",
        type: "package_selection",
        name: "offer",
        label: "اختر العرض",
        order: 1,
        isVisible: true,
        isRequired: true,
        validation: { required: true },
        options: [
          {
            id: "offer_1",
            label: "بطاقة 50 دولار",
            value: "50",
            price: 2500,
            originalPrice: 2700,
            discount: 7
          },
          {
            id: "offer_2",
            label: "بطاقة 100 دولار",
            value: "100",
            price: 4800,
            originalPrice: 5200,
            discount: 8
          }
        ]
      },
      {
        id: "field_region",
        type: "region_selection",
        name: "region",
        label: "اختر المنطقة",
        order: 2,
        isVisible: true,
        isRequired: true,
        validation: { required: true },
        options: [
          { id: "region_1", label: "عالمي", value: "global" },
          { id: "region_2", label: "الولايات المتحدة", value: "us" },
          { id: "region_3", label: "أوروبا", value: "eu" }
        ]
      },
      {
        id: "field_email",
        type: "email",
        name: "email",
        label: "البريد الإلكتروني",
        placeholder: "<EMAIL>",
        order: 3,
        isVisible: true,
        isRequired: true,
        validation: { required: true }
      }
    ]
  }
]

// Mock Products
export const mockCMSProducts: DynamicProduct[] = [
  {
    id: "product_free_fire",
    templateId: "template_simple_package",
    slug: "free-fire-diamonds",
    title: "شحن جواهر Free Fire",
    shortDescription: "شحن فوري للجواهر في لعبة Free Fire - احصل على الجواهر فوراً",
    image: "/images/products/free-fire.jpg",
    category: "ألعاب الموبايل",
    rating: 4.7,
    reviewsCount: 980,
    estimatedTime: "أقل من دقيقة",
    inStock: true,
    isActive: true,
    metadata: {
      featured: true,
      popular: true,
      tags: ["free fire", "جواهر", "ألعاب", "موبايل"]
    },
    createdAt: new Date("2024-01-10"),
    updatedAt: new Date("2024-01-15"),
    fields: mockTemplates[0].fields
  },
  {
    id: "product_tiktok_coins",
    templateId: "template_complex_form",
    slug: "tiktok-coins",
    title: "شحن عملات تيك توك | TikTok Coins",
    shortDescription: "شحن عملات TikTok للهدايا والمحتوى الحصري",
    image: "/images/products/tiktok.jpg",
    category: "شبكات اجتماعية",
    rating: 4.5,
    reviewsCount: 650,
    priceRange: "25 - 2000",
    inStock: true,
    isActive: true,
    metadata: {
      featured: false,
      popular: true,
      tags: ["tiktok", "عملات", "هدايا", "شبكات اجتماعية"]
    },
    createdAt: new Date("2024-01-12"),
    updatedAt: new Date("2024-01-18"),
    fields: mockTemplates[1].fields
  },
  {
    id: "product_steam_wallet",
    templateId: "template_standard_product",
    slug: "steam-wallet",
    title: "بطاقة Steam Wallet",
    shortDescription: "بطاقات شحن محفظة Steam للألعاب والمحتوى الرقمي",
    fullDescription: "بطاقات Steam Wallet هي الطريقة المثالية لشحن محفظتك على منصة Steam",
    image: "/images/products/steam-wallet.jpg",
    category: "بطاقات الألعاب",
    rating: 4.9,
    reviewsCount: 2150,
    priceRange: "50 - 500",
    inStock: true,
    isActive: true,
    metadata: {
      featured: true,
      popular: false,
      tags: ["steam", "بطاقات", "ألعاب", "محفظة"]
    },
    createdAt: new Date("2024-01-08"),
    updatedAt: new Date("2024-01-20"),
    fields: mockTemplates[2].fields
  }
]

// Mock Categories
export const mockCategories = [
  { id: "mobile_games", name: "ألعاب الموبايل", count: 8 },
  { id: "pc_games", name: "ألعاب الكمبيوتر", count: 5 },
  { id: "game_cards", name: "بطاقات الألعاب", count: 3 },
  { id: "subscriptions", name: "اشتراكات الألعاب", count: 4 },
  { id: "social_media", name: "شبكات اجتماعية", count: 2 }
]

// Mock Analytics Data
export const mockAnalytics = {
  totalProducts: 15,
  activeProducts: 12,
  totalTemplates: 5,
  totalViews: 12450,
  totalConversions: 890,
  conversionRate: 7.15,
  topProducts: [
    { id: "product_free_fire", name: "شحن جواهر Free Fire", views: 3200, conversions: 245 },
    { id: "product_tiktok_coins", name: "شحن عملات تيك توك", views: 2800, conversions: 180 },
    { id: "product_steam_wallet", name: "بطاقة Steam Wallet", views: 2100, conversions: 165 }
  ],
  recentActivity: [
    { type: "product_created", message: "تم إنشاء منتج جديد: شحن UC PUBG", timestamp: new Date() },
    { type: "template_updated", message: "تم تحديث قالب الباقات البسيط", timestamp: new Date() },
    { type: "product_activated", message: "تم تفعيل منتج: بطاقة PlayStation", timestamp: new Date() }
  ]
}
