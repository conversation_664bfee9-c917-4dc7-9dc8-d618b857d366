"use client"

import { useState } from "react"
import { use<PERSON><PERSON><PERSON> } from "next/navigation"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { AdminLayout } from "@/components/admin/AdminLayout"
import { mockTemplates } from "@/lib/data/mockCMSData"
import { ProductTemplate } from "@/lib/types"
import { 
  Plus, 
  FolderOpen, 
  Edit, 
  Copy, 
  Trash2, 
  Eye,
  Settings,
  Calendar,
  Layers
} from "lucide-react"

export default function TemplatesPage() {
  const router = useRouter()
  const [templates, setTemplates] = useState<ProductTemplate[]>(mockTemplates)

  const handleCreateTemplate = () => {
    router.push("/admin/templates/create")
  }

  const handleEditTemplate = (template: ProductTemplate) => {
    router.push(`/admin/templates/${template.id}/edit`)
  }

  const handleDuplicateTemplate = (template: ProductTemplate) => {
    const duplicatedTemplate: ProductTemplate = {
      ...template,
      id: `template_${Date.now()}`,
      name: `${template.name} - نسخة`,
      createdAt: new Date(),
      updatedAt: new Date()
    }
    setTemplates(prev => [...prev, duplicatedTemplate])
  }

  const handleDeleteTemplate = (templateId: string) => {
    if (confirm("هل أنت متأكد من حذف هذا القالب؟")) {
      setTemplates(prev => prev.filter(t => t.id !== templateId))
    }
  }

  const handleToggleActive = (templateId: string) => {
    setTemplates(prev => prev.map(template =>
      template.id === templateId 
        ? { ...template, isActive: !template.isActive }
        : template
    ))
  }

  return (
    <AdminLayout 
      title="قوالب المنتجات" 
      subtitle={`إدارة القوالب القابلة للإعادة الاستخدام (${templates.length} قالب)`}
      actions={
        <Button 
          onClick={handleCreateTemplate}
          className="bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white font-bold"
        >
          <Plus className="h-4 w-4 ml-2" />
          قالب جديد
        </Button>
      }
    >
      <div className="space-y-6">
        {/* Templates Stats */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card className="bg-slate-800/50 border-slate-700/50 backdrop-blur-sm">
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-slate-400 text-sm">إجمالي القوالب</p>
                  <p className="text-2xl font-bold text-white">{templates.length}</p>
                </div>
                <FolderOpen className="h-8 w-8 text-blue-400" />
              </div>
            </CardContent>
          </Card>

          <Card className="bg-slate-800/50 border-slate-700/50 backdrop-blur-sm">
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-slate-400 text-sm">القوالب النشطة</p>
                  <p className="text-2xl font-bold text-white">
                    {templates.filter(t => t.isActive).length}
                  </p>
                </div>
                <Eye className="h-8 w-8 text-green-400" />
              </div>
            </CardContent>
          </Card>

          <Card className="bg-slate-800/50 border-slate-700/50 backdrop-blur-sm">
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-slate-400 text-sm">متوسط الحقول</p>
                  <p className="text-2xl font-bold text-white">
                    {Math.round(templates.reduce((acc, t) => acc + t.fields.length, 0) / templates.length || 0)}
                  </p>
                </div>
                <Layers className="h-8 w-8 text-purple-400" />
              </div>
            </CardContent>
          </Card>

          <Card className="bg-slate-800/50 border-slate-700/50 backdrop-blur-sm">
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-slate-400 text-sm">تم إنشاؤها اليوم</p>
                  <p className="text-2xl font-bold text-white">
                    {templates.filter(t => 
                      new Date(t.createdAt).toDateString() === new Date().toDateString()
                    ).length}
                  </p>
                </div>
                <Calendar className="h-8 w-8 text-yellow-400" />
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Templates Grid */}
        {templates.length === 0 ? (
          <Card className="bg-slate-800/50 border-slate-700/50 backdrop-blur-sm">
            <CardContent className="p-12 text-center">
              <div className="text-6xl mb-4">📝</div>
              <h3 className="text-xl font-bold text-white mb-2">لا توجد قوالب بعد</h3>
              <p className="text-slate-400 mb-6">
                ابدأ بإنشاء قالب جديد لتسريع عملية إنشاء المنتجات
              </p>
              <Button 
                onClick={handleCreateTemplate}
                className="bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700"
              >
                <Plus className="h-4 w-4 ml-2" />
                إنشاء أول قالب
              </Button>
            </CardContent>
          </Card>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {templates.map((template) => (
              <Card 
                key={template.id} 
                className="bg-slate-800/50 border-slate-700/50 backdrop-blur-sm hover:bg-slate-700/50 transition-all duration-300 group"
              >
                <CardHeader className="pb-3">
                  <div className="flex items-start justify-between">
                    <div className="flex items-center gap-3">
                      <div className="p-2 bg-slate-700 rounded-lg">
                        <FolderOpen className="h-5 w-5 text-slate-300" />
                      </div>
                      <div>
                        <CardTitle className="text-white text-lg">{template.name}</CardTitle>
                        <p className="text-slate-400 text-sm">{template.description}</p>
                      </div>
                    </div>
                    
                    <div className="flex items-center gap-1">
                      <Badge 
                        className={template.isActive 
                          ? "bg-green-500/20 text-green-400" 
                          : "bg-slate-500/20 text-slate-400"
                        }
                      >
                        {template.isActive ? "نشط" : "غير نشط"}
                      </Badge>
                    </div>
                  </div>
                </CardHeader>

                <CardContent className="space-y-4">
                  {/* Template Stats */}
                  <div className="grid grid-cols-3 gap-3 text-center">
                    <div className="p-2 bg-slate-700/30 rounded-lg">
                      <p className="text-white font-bold">{template.fields.length}</p>
                      <p className="text-slate-400 text-xs">حقل</p>
                    </div>
                    <div className="p-2 bg-slate-700/30 rounded-lg">
                      <p className="text-white font-bold">
                        {template.fields.filter(f => f.isRequired).length}
                      </p>
                      <p className="text-slate-400 text-xs">مطلوب</p>
                    </div>
                    <div className="p-2 bg-slate-700/30 rounded-lg">
                      <p className="text-white font-bold">{template.layout}</p>
                      <p className="text-slate-400 text-xs">تخطيط</p>
                    </div>
                  </div>

                  {/* Field Types Preview */}
                  <div>
                    <p className="text-slate-400 text-xs mb-2">أنواع الحقول:</p>
                    <div className="flex flex-wrap gap-1">
                      {Array.from(new Set(template.fields.map(f => f.type))).map(type => (
                        <Badge 
                          key={type} 
                          variant="secondary" 
                          className="bg-slate-700 text-slate-300 text-xs"
                        >
                          {type}
                        </Badge>
                      ))}
                    </div>
                  </div>

                  {/* Dates */}
                  <div className="text-xs text-slate-400 space-y-1">
                    <p>تم الإنشاء: {new Date(template.createdAt).toLocaleDateString('ar-EG')}</p>
                    <p>آخر تحديث: {new Date(template.updatedAt).toLocaleDateString('ar-EG')}</p>
                  </div>

                  {/* Actions */}
                  <div className="flex items-center gap-2 pt-2 border-t border-slate-700">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleEditTemplate(template)}
                      className="flex-1 text-blue-400 hover:text-blue-300"
                    >
                      <Edit className="h-4 w-4 mr-1" />
                      تعديل
                    </Button>

                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleDuplicateTemplate(template)}
                      className="text-green-400 hover:text-green-300"
                    >
                      <Copy className="h-4 w-4" />
                    </Button>

                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleToggleActive(template.id)}
                      className="text-yellow-400 hover:text-yellow-300"
                    >
                      <Settings className="h-4 w-4" />
                    </Button>

                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleDeleteTemplate(template.id)}
                      className="text-red-400 hover:text-red-300"
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        )}
      </div>
    </AdminLayout>
  )
}
