"use client"

import { useState } from "react"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { DynamicField, FieldType } from "@/lib/types"
import { FIELD_TYPES } from "@/lib/types/cms"
import { createDefaultField } from "@/lib/utils/cms"
import { 
  Plus, 
  Type, 
  Mail, 
  Hash, 
  AlignLeft, 
  ChevronDown, 
  Package, 
  Server, 
  Image,
  Grip,
  Settings,
  Copy,
  Trash2
} from "lucide-react"

interface FieldBuilderProps {
  fields: DynamicField[]
  onFieldsChange: (fields: DynamicField[]) => void
  onFieldSelect?: (field: DynamicField) => void
  selectedFieldId?: string
}

const fieldIcons: Record<FieldType, React.ReactNode> = {
  text: <Type className="h-4 w-4" />,
  email: <Mail className="h-4 w-4" />,
  number: <Hash className="h-4 w-4" />,
  textarea: <AlignLeft className="h-4 w-4" />,
  select: <ChevronDown className="h-4 w-4" />,
  radio: <ChevronDown className="h-4 w-4" />,
  checkbox: <ChevronDown className="h-4 w-4" />,
  image: <Image className="h-4 w-4" />,
  package_selection: <Package className="h-4 w-4" />,
  server_selection: <Server className="h-4 w-4" />,
  region_selection: <Server className="h-4 w-4" />,
  custom_info: <Type className="h-4 w-4" />
}

export function FieldBuilder({ 
  fields, 
  onFieldsChange, 
  onFieldSelect, 
  selectedFieldId 
}: FieldBuilderProps) {
  const [draggedField, setDraggedField] = useState<string | null>(null)

  const addField = (type: FieldType) => {
    const newField = createDefaultField(type, fields.length)
    const updatedFields = [...fields, newField]
    onFieldsChange(updatedFields)
    onFieldSelect?.(newField)
  }

  const duplicateField = (field: DynamicField) => {
    const duplicatedField = {
      ...field,
      id: `field_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      name: `${field.name}_copy`,
      label: `${field.label} - نسخة`,
      order: fields.length
    }
    const updatedFields = [...fields, duplicatedField]
    onFieldsChange(updatedFields)
  }

  const deleteField = (fieldId: string) => {
    const updatedFields = fields
      .filter(f => f.id !== fieldId)
      .map((f, index) => ({ ...f, order: index }))
    onFieldsChange(updatedFields)
  }

  const handleDragStart = (fieldId: string) => {
    setDraggedField(fieldId)
  }

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault()
  }

  const handleDrop = (e: React.DragEvent, targetFieldId: string) => {
    e.preventDefault()
    
    if (!draggedField || draggedField === targetFieldId) {
      setDraggedField(null)
      return
    }

    const draggedIndex = fields.findIndex(f => f.id === draggedField)
    const targetIndex = fields.findIndex(f => f.id === targetFieldId)

    if (draggedIndex === -1 || targetIndex === -1) {
      setDraggedField(null)
      return
    }

    const newFields = [...fields]
    const [draggedFieldObj] = newFields.splice(draggedIndex, 1)
    newFields.splice(targetIndex, 0, draggedFieldObj)

    // Update order values
    const reorderedFields = newFields.map((field, index) => ({
      ...field,
      order: index
    }))

    onFieldsChange(reorderedFields)
    setDraggedField(null)
  }

  return (
    <div className="space-y-6">
      {/* Field Types Palette */}
      <Card className="bg-slate-800/50 border-slate-700/50 backdrop-blur-sm">
        <CardHeader>
          <CardTitle className="text-white flex items-center gap-2">
            <Plus className="h-5 w-5" />
            إضافة حقول جديدة
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3">
            {FIELD_TYPES.map((fieldType) => (
              <Button
                key={fieldType.id}
                variant="ghost"
                onClick={() => addField(fieldType.type)}
                className="h-auto p-4 flex flex-col items-center gap-2 text-center hover:bg-slate-700/50 border border-slate-600/30 hover:border-slate-500/50 transition-all"
              >
                <div className="p-2 bg-slate-700/50 rounded-lg">
                  {fieldIcons[fieldType.type]}
                </div>
                <div>
                  <p className="text-white text-sm font-medium">{fieldType.label}</p>
                  <p className="text-slate-400 text-xs">{fieldType.description}</p>
                </div>
              </Button>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Fields List */}
      <Card className="bg-slate-800/50 border-slate-700/50 backdrop-blur-sm">
        <CardHeader>
          <CardTitle className="text-white flex items-center justify-between">
            <span>حقول المنتج ({fields.length})</span>
            {fields.length > 0 && (
              <Badge variant="secondary" className="bg-slate-700 text-slate-300">
                اسحب لإعادة الترتيب
              </Badge>
            )}
          </CardTitle>
        </CardHeader>
        <CardContent>
          {fields.length === 0 ? (
            <div className="text-center py-12">
              <div className="text-6xl mb-4">📝</div>
              <h3 className="text-xl font-bold text-white mb-2">لا توجد حقول بعد</h3>
              <p className="text-slate-400 mb-6">
                ابدأ بإضافة حقول من الأعلى لبناء نموذج المنتج
              </p>
            </div>
          ) : (
            <div className="space-y-3">
              {fields
                .sort((a, b) => a.order - b.order)
                .map((field) => (
                  <div
                    key={field.id}
                    draggable
                    onDragStart={() => handleDragStart(field.id)}
                    onDragOver={handleDragOver}
                    onDrop={(e) => handleDrop(e, field.id)}
                    className={`
                      p-4 bg-slate-700/30 rounded-lg border transition-all cursor-move
                      ${selectedFieldId === field.id 
                        ? 'border-yellow-400 bg-yellow-400/10' 
                        : 'border-slate-600/50 hover:border-slate-500/50'
                      }
                      ${draggedField === field.id ? 'opacity-50' : ''}
                    `}
                    onClick={() => onFieldSelect?.(field)}
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        <div className="p-2 bg-slate-600/50 rounded-lg cursor-grab active:cursor-grabbing">
                          <Grip className="h-4 w-4 text-slate-400" />
                        </div>
                        
                        <div className="flex items-center gap-2">
                          {fieldIcons[field.type]}
                          <div>
                            <p className="text-white font-medium">{field.label}</p>
                            <p className="text-slate-400 text-sm">
                              {FIELD_TYPES.find(ft => ft.type === field.type)?.name || field.type}
                            </p>
                          </div>
                        </div>

                        {field.isRequired && (
                          <Badge className="bg-red-500/20 text-red-400 text-xs">
                            مطلوب
                          </Badge>
                        )}

                        {!field.isVisible && (
                          <Badge className="bg-slate-500/20 text-slate-400 text-xs">
                            مخفي
                          </Badge>
                        )}
                      </div>

                      <div className="flex items-center gap-2">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={(e) => {
                            e.stopPropagation()
                            onFieldSelect?.(field)
                          }}
                          className="text-slate-400 hover:text-white"
                        >
                          <Settings className="h-4 w-4" />
                        </Button>
                        
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={(e) => {
                            e.stopPropagation()
                            duplicateField(field)
                          }}
                          className="text-slate-400 hover:text-blue-400"
                        >
                          <Copy className="h-4 w-4" />
                        </Button>
                        
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={(e) => {
                            e.stopPropagation()
                            deleteField(field.id)
                          }}
                          className="text-slate-400 hover:text-red-400"
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>

                    {/* Field Preview */}
                    <div className="mt-3 p-3 bg-slate-800/50 rounded-lg">
                      <p className="text-slate-400 text-xs mb-2">معاينة:</p>
                      <div className="text-sm">
                        {field.type === "text" && (
                          <input 
                            type="text" 
                            placeholder={field.placeholder} 
                            className="w-full p-2 bg-slate-700 border border-slate-600 rounded text-white text-sm"
                            disabled
                          />
                        )}
                        {field.type === "select" && (
                          <select className="w-full p-2 bg-slate-700 border border-slate-600 rounded text-white text-sm" disabled>
                            <option>{field.placeholder || "اختر..."}</option>
                            {field.options?.map(option => (
                              <option key={option.id}>{option.label}</option>
                            ))}
                          </select>
                        )}
                        {field.type === "package_selection" && (
                          <div className="grid grid-cols-2 gap-2">
                            {field.options?.slice(0, 2).map(option => (
                              <div key={option.id} className="p-2 bg-slate-600 rounded text-center">
                                <p className="text-white text-xs">{option.label}</p>
                                {option.price && (
                                  <p className="text-yellow-400 text-xs">{option.price} ج.س</p>
                                )}
                              </div>
                            ))}
                          </div>
                        )}
                        {field.type === "textarea" && (
                          <textarea 
                            placeholder={field.placeholder}
                            className="w-full p-2 bg-slate-700 border border-slate-600 rounded text-white text-sm h-20 resize-none"
                            disabled
                          />
                        )}
                        {field.type === "image" && (
                          <div className="w-full h-20 bg-slate-700 border border-slate-600 rounded flex items-center justify-center">
                            <Image className="h-6 w-6 text-slate-400" />
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
