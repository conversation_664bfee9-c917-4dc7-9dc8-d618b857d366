"use client"

import { useState, useEffect } from "react"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Badge } from "@/components/ui/badge"
import { DynamicField, FieldOption } from "@/lib/types"
import { FIELD_TYPES } from "@/lib/types/cms"
import { 
  X, 
  Plus, 
  Trash2, 
  Eye, 
  EyeOff, 
  AlertCircle,
  Settings,
  Save,
  RotateCcw
} from "lucide-react"

interface FieldConfigPanelProps {
  field: DynamicField | null
  onUpdate: (field: DynamicField) => void
  onClose: () => void
  isOpen: boolean
}

export function FieldConfigPanel({ field, onUpdate, onClose, isOpen }: FieldConfigPanelProps) {
  const [localField, setLocalField] = useState<DynamicField | null>(null)
  const [hasC<PERSON><PERSON>, setHasChanges] = useState(false)

  useEffect(() => {
    if (field) {
      setLocalField({ ...field })
      setHasChanges(false)
    }
  }, [field])

  if (!isOpen || !field || !localField) {
    return null
  }

  const fieldType = FIELD_TYPES.find(ft => ft.type === field.type)

  const handleFieldChange = (updates: Partial<DynamicField>) => {
    const updatedField = { ...localField, ...updates }
    setLocalField(updatedField)
    setHasChanges(true)
  }

  const handleSave = () => {
    if (localField) {
      onUpdate(localField)
      setHasChanges(false)
    }
  }

  const handleReset = () => {
    if (field) {
      setLocalField({ ...field })
      setHasChanges(false)
    }
  }

  const addOption = () => {
    const newOption: FieldOption = {
      id: `option_${Date.now()}`,
      label: "خيار جديد",
      value: `option_${Date.now()}`,
      price: localField.type === "package_selection" ? 0 : undefined
    }
    
    const updatedOptions = [...(localField.options || []), newOption]
    handleFieldChange({ options: updatedOptions })
  }

  const updateOption = (optionId: string, updates: Partial<FieldOption>) => {
    const updatedOptions = localField.options?.map(option =>
      option.id === optionId ? { ...option, ...updates } : option
    ) || []
    
    handleFieldChange({ options: updatedOptions })
  }

  const deleteOption = (optionId: string) => {
    const updatedOptions = localField.options?.filter(option => option.id !== optionId) || []
    handleFieldChange({ options: updatedOptions })
  }

  const hasOptions = ["select", "radio", "checkbox", "package_selection", "server_selection", "region_selection"].includes(localField.type)

  return (
    <div className="fixed inset-0 bg-black/50 z-50 flex items-center justify-center p-4">
      <Card className="bg-slate-800 border-slate-700 w-full max-w-2xl max-h-[90vh] overflow-hidden">
        <CardHeader className="border-b border-slate-700">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-slate-700 rounded-lg">
                <Settings className="h-5 w-5 text-slate-300" />
              </div>
              <div>
                <CardTitle className="text-white">إعدادات الحقل</CardTitle>
                <p className="text-slate-400 text-sm">{fieldType?.name}</p>
              </div>
            </div>
            <div className="flex items-center gap-2">
              {hasChanges && (
                <Badge className="bg-yellow-500/20 text-yellow-400">
                  تغييرات غير محفوظة
                </Badge>
              )}
              <Button variant="ghost" size="sm" onClick={onClose}>
                <X className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </CardHeader>

        <CardContent className="p-6 overflow-y-auto max-h-[calc(90vh-120px)]">
          <div className="space-y-6">
            {/* Basic Settings */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold text-white">الإعدادات الأساسية</h3>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label className="text-slate-300">تسمية الحقل</Label>
                  <Input
                    value={localField.label}
                    onChange={(e) => handleFieldChange({ label: e.target.value })}
                    className="bg-slate-700 border-slate-600 text-white"
                    placeholder="أدخل تسمية الحقل"
                  />
                </div>

                <div>
                  <Label className="text-slate-300">اسم الحقل (تقني)</Label>
                  <Input
                    value={localField.name}
                    onChange={(e) => handleFieldChange({ name: e.target.value })}
                    className="bg-slate-700 border-slate-600 text-white"
                    placeholder="field_name"
                  />
                </div>
              </div>

              {localField.type !== "image" && (
                <div>
                  <Label className="text-slate-300">النص التوضيحي</Label>
                  <Input
                    value={localField.placeholder || ""}
                    onChange={(e) => handleFieldChange({ placeholder: e.target.value })}
                    className="bg-slate-700 border-slate-600 text-white"
                    placeholder="أدخل النص التوضيحي"
                  />
                </div>
              )}

              <div>
                <Label className="text-slate-300">الوصف</Label>
                <Input
                  value={localField.description || ""}
                  onChange={(e) => handleFieldChange({ description: e.target.value })}
                  className="bg-slate-700 border-slate-600 text-white"
                  placeholder="وصف مساعد للحقل"
                />
              </div>

              {/* Field State Toggles */}
              <div className="flex flex-wrap gap-4">
                <div className="flex items-center gap-2">
                  <Button
                    variant={localField.isRequired ? "default" : "ghost"}
                    size="sm"
                    onClick={() => handleFieldChange({ isRequired: !localField.isRequired })}
                    className={localField.isRequired ? "bg-red-500 hover:bg-red-600" : ""}
                  >
                    <AlertCircle className="h-4 w-4 mr-1" />
                    مطلوب
                  </Button>
                </div>

                <div className="flex items-center gap-2">
                  <Button
                    variant={localField.isVisible ? "default" : "ghost"}
                    size="sm"
                    onClick={() => handleFieldChange({ isVisible: !localField.isVisible })}
                    className={localField.isVisible ? "bg-green-500 hover:bg-green-600" : ""}
                  >
                    {localField.isVisible ? <Eye className="h-4 w-4 mr-1" /> : <EyeOff className="h-4 w-4 mr-1" />}
                    {localField.isVisible ? "مرئي" : "مخفي"}
                  </Button>
                </div>
              </div>
            </div>

            {/* Validation Settings */}
            {(localField.type === "text" || localField.type === "textarea" || localField.type === "number") && (
              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-white">قواعد التحقق</h3>
                
                {(localField.type === "text" || localField.type === "textarea") && (
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label className="text-slate-300">أقل عدد أحرف</Label>
                      <Input
                        type="number"
                        value={localField.validation.minLength || ""}
                        onChange={(e) => handleFieldChange({ 
                          validation: { 
                            ...localField.validation, 
                            minLength: e.target.value ? parseInt(e.target.value) : undefined 
                          }
                        })}
                        className="bg-slate-700 border-slate-600 text-white"
                        placeholder="0"
                      />
                    </div>

                    <div>
                      <Label className="text-slate-300">أكبر عدد أحرف</Label>
                      <Input
                        type="number"
                        value={localField.validation.maxLength || ""}
                        onChange={(e) => handleFieldChange({ 
                          validation: { 
                            ...localField.validation, 
                            maxLength: e.target.value ? parseInt(e.target.value) : undefined 
                          }
                        })}
                        className="bg-slate-700 border-slate-600 text-white"
                        placeholder="100"
                      />
                    </div>
                  </div>
                )}

                {localField.type === "number" && (
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label className="text-slate-300">أقل قيمة</Label>
                      <Input
                        type="number"
                        value={localField.validation.min || ""}
                        onChange={(e) => handleFieldChange({ 
                          validation: { 
                            ...localField.validation, 
                            min: e.target.value ? parseInt(e.target.value) : undefined 
                          }
                        })}
                        className="bg-slate-700 border-slate-600 text-white"
                        placeholder="0"
                      />
                    </div>

                    <div>
                      <Label className="text-slate-300">أكبر قيمة</Label>
                      <Input
                        type="number"
                        value={localField.validation.max || ""}
                        onChange={(e) => handleFieldChange({ 
                          validation: { 
                            ...localField.validation, 
                            max: e.target.value ? parseInt(e.target.value) : undefined 
                          }
                        })}
                        className="bg-slate-700 border-slate-600 text-white"
                        placeholder="999999"
                      />
                    </div>
                  </div>
                )}

                <div>
                  <Label className="text-slate-300">رسالة خطأ مخصصة</Label>
                  <Input
                    value={localField.validation.customMessage || ""}
                    onChange={(e) => handleFieldChange({ 
                      validation: { 
                        ...localField.validation, 
                        customMessage: e.target.value 
                      }
                    })}
                    className="bg-slate-700 border-slate-600 text-white"
                    placeholder="رسالة الخطأ التي ستظهر للمستخدم"
                  />
                </div>
              </div>
            )}

            {/* Options Settings */}
            {hasOptions && (
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <h3 className="text-lg font-semibold text-white">الخيارات</h3>
                  <Button onClick={addOption} size="sm" className="bg-green-500 hover:bg-green-600">
                    <Plus className="h-4 w-4 mr-1" />
                    إضافة خيار
                  </Button>
                </div>

                <div className="space-y-3">
                  {localField.options?.map((option, index) => (
                    <div key={option.id} className="p-4 bg-slate-700/50 rounded-lg border border-slate-600">
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                        <div>
                          <Label className="text-slate-300 text-xs">التسمية</Label>
                          <Input
                            value={option.label}
                            onChange={(e) => updateOption(option.id, { label: e.target.value })}
                            className="bg-slate-600 border-slate-500 text-white text-sm"
                            placeholder="تسمية الخيار"
                          />
                        </div>

                        <div>
                          <Label className="text-slate-300 text-xs">القيمة</Label>
                          <Input
                            value={option.value}
                            onChange={(e) => updateOption(option.id, { value: e.target.value })}
                            className="bg-slate-600 border-slate-500 text-white text-sm"
                            placeholder="قيمة الخيار"
                          />
                        </div>

                        {localField.type === "package_selection" && (
                          <>
                            <div>
                              <Label className="text-slate-300 text-xs">السعر</Label>
                              <Input
                                type="number"
                                value={option.price || ""}
                                onChange={(e) => updateOption(option.id, { 
                                  price: e.target.value ? parseFloat(e.target.value) : undefined 
                                })}
                                className="bg-slate-600 border-slate-500 text-white text-sm"
                                placeholder="0"
                              />
                            </div>

                            <div>
                              <Label className="text-slate-300 text-xs">السعر الأصلي</Label>
                              <Input
                                type="number"
                                value={option.originalPrice || ""}
                                onChange={(e) => updateOption(option.id, { 
                                  originalPrice: e.target.value ? parseFloat(e.target.value) : undefined 
                                })}
                                className="bg-slate-600 border-slate-500 text-white text-sm"
                                placeholder="0"
                              />
                            </div>
                          </>
                        )}
                      </div>

                      <div className="flex items-center justify-between mt-3">
                        <div className="flex items-center gap-2">
                          {localField.type === "package_selection" && (
                            <Button
                              variant={option.popular ? "default" : "ghost"}
                              size="sm"
                              onClick={() => updateOption(option.id, { popular: !option.popular })}
                              className={option.popular ? "bg-yellow-500 hover:bg-yellow-600 text-slate-900" : ""}
                            >
                              الأكثر طلباً
                            </Button>
                          )}
                        </div>

                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => deleteOption(option.id)}
                          className="text-red-400 hover:text-red-300"
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  ))}

                  {(!localField.options || localField.options.length === 0) && (
                    <div className="text-center py-8 text-slate-400">
                      <p>لا توجد خيارات بعد</p>
                      <p className="text-sm">اضغط "إضافة خيار" لبدء إضافة الخيارات</p>
                    </div>
                  )}
                </div>
              </div>
            )}
          </div>
        </CardContent>

        {/* Footer Actions */}
        <div className="border-t border-slate-700 p-4 flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Button
              variant="ghost"
              onClick={handleReset}
              disabled={!hasChanges}
              className="text-slate-400 hover:text-white"
            >
              <RotateCcw className="h-4 w-4 mr-1" />
              إعادة تعيين
            </Button>
          </div>

          <div className="flex items-center gap-2">
            <Button variant="ghost" onClick={onClose}>
              إلغاء
            </Button>
            <Button 
              onClick={handleSave}
              disabled={!hasChanges}
              className="bg-green-500 hover:bg-green-600"
            >
              <Save className="h-4 w-4 mr-1" />
              حفظ التغييرات
            </Button>
          </div>
        </div>
      </Card>
    </div>
  )
}
