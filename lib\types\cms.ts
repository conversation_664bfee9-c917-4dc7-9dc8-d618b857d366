// CMS-specific types for the dynamic product management system

import { DynamicField, FieldType, ProductTemplate, DynamicProduct } from "./index"

// Field Builder Types
export interface FieldBuilder {
  id: string
  type: FieldType
  name: string
  label: string
  icon: string
  description: string
  defaultConfig: Partial<DynamicField>
  configurable: string[] // Which properties can be configured
}

// Admin CMS Interface Types
export interface CMSState {
  currentTemplate: ProductTemplate | null
  currentProduct: DynamicProduct | null
  templates: ProductTemplate[]
  products: DynamicProduct[]
  isLoading: boolean
  error: string | null
}

export interface CMSAction {
  type: string
  payload?: any
}

// Field Configuration Panel Types
export interface FieldConfigPanelProps {
  field: DynamicField
  onUpdate: (field: DynamicField) => void
  onDelete: (fieldId: string) => void
  onDuplicate: (field: DynamicField) => void
  isOpen: boolean
  onClose: () => void
}

// Drag and Drop Types
export interface DragItem {
  id: string
  type: "field"
  field: DynamicField
  index: number
}

export interface DropResult {
  dragIndex: number
  hoverIndex: number
}

// Template Management Types
export interface TemplateManagerProps {
  templates: ProductTemplate[]
  onSelect: (template: ProductTemplate) => void
  onCreate: () => void
  onEdit: (template: ProductTemplate) => void
  onDelete: (templateId: string) => void
  onDuplicate: (template: ProductTemplate) => void
}

// Product Management Types
export interface ProductManagerProps {
  products: DynamicProduct[]
  templates: ProductTemplate[]
  onSelect: (product: DynamicProduct) => void
  onCreate: (templateId?: string) => void
  onEdit: (product: DynamicProduct) => void
  onDelete: (productId: string) => void
  onDuplicate: (product: DynamicProduct) => void
  onToggleActive: (productId: string, isActive: boolean) => void
}

// Field Validation Types
export interface ValidationResult {
  isValid: boolean
  errors: Record<string, string>
}

export interface ValidationRule {
  field: string
  rule: string
  message: string
  params?: any[]
}

// Preview Types
export interface ProductPreviewProps {
  product: DynamicProduct
  formData: Record<string, any>
  onFormChange: (data: Record<string, any>) => void
  mode: "desktop" | "mobile"
}

// Import/Export Types
export interface ExportData {
  templates: ProductTemplate[]
  products: DynamicProduct[]
  version: string
  exportedAt: Date
}

export interface ImportResult {
  success: boolean
  imported: {
    templates: number
    products: number
  }
  errors: string[]
  warnings: string[]
}

// Analytics Types
export interface ProductAnalytics {
  productId: string
  views: number
  conversions: number
  revenue: number
  averageOrderValue: number
  topFields: Array<{
    fieldName: string
    interactions: number
  }>
  period: "day" | "week" | "month" | "year"
}

// Search and Filter Types
export interface ProductFilter {
  category?: string
  template?: string
  status?: "active" | "inactive" | "all"
  featured?: boolean
  popular?: boolean
  dateRange?: {
    start: Date
    end: Date
  }
}

export interface ProductSearchResult {
  products: DynamicProduct[]
  total: number
  page: number
  limit: number
  filters: ProductFilter
}

// Bulk Operations Types
export interface BulkOperation {
  type: "activate" | "deactivate" | "delete" | "duplicate" | "update_category" | "update_template"
  productIds: string[]
  params?: Record<string, any>
}

export interface BulkOperationResult {
  success: boolean
  processed: number
  failed: number
  errors: Array<{
    productId: string
    error: string
  }>
}

// Field Type Definitions for Builder
export const FIELD_TYPES: FieldBuilder[] = [
  {
    id: "text",
    type: "text",
    name: "Text Input",
    label: "نص",
    icon: "Type",
    description: "حقل إدخال نص بسيط",
    defaultConfig: {
      validation: { required: false, minLength: 1, maxLength: 100 }
    },
    configurable: ["label", "placeholder", "validation", "defaultValue"]
  },
  {
    id: "email",
    type: "email",
    name: "Email Input",
    label: "بريد إلكتروني",
    icon: "Mail",
    description: "حقل إدخال البريد الإلكتروني",
    defaultConfig: {
      validation: { required: false, pattern: "^[^@]+@[^@]+\\.[^@]+$" }
    },
    configurable: ["label", "placeholder", "validation"]
  },
  {
    id: "number",
    type: "number",
    name: "Number Input",
    label: "رقم",
    icon: "Hash",
    description: "حقل إدخال رقمي",
    defaultConfig: {
      validation: { required: false, min: 0, max: 999999 }
    },
    configurable: ["label", "placeholder", "validation", "defaultValue"]
  },
  {
    id: "textarea",
    type: "textarea",
    name: "Text Area",
    label: "نص طويل",
    icon: "AlignLeft",
    description: "حقل إدخال نص متعدد الأسطر",
    defaultConfig: {
      validation: { required: false, minLength: 1, maxLength: 500 }
    },
    configurable: ["label", "placeholder", "validation", "defaultValue"]
  },
  {
    id: "select",
    type: "select",
    name: "Dropdown Select",
    label: "قائمة منسدلة",
    icon: "ChevronDown",
    description: "قائمة اختيار منسدلة",
    defaultConfig: {
      validation: { required: false },
      options: []
    },
    configurable: ["label", "validation", "options", "defaultValue"]
  },
  {
    id: "package_selection",
    type: "package_selection",
    name: "Package Selection",
    label: "اختيار الباقة",
    icon: "Package",
    description: "أزرار اختيار الباقات مع الأسعار",
    defaultConfig: {
      validation: { required: true },
      options: []
    },
    configurable: ["label", "validation", "options"]
  },
  {
    id: "server_selection",
    type: "server_selection",
    name: "Server Selection",
    label: "اختيار السيرفر",
    icon: "Server",
    description: "قائمة اختيار السيرفر",
    defaultConfig: {
      validation: { required: true },
      options: []
    },
    configurable: ["label", "validation", "options", "defaultValue"]
  },
  {
    id: "image",
    type: "image",
    name: "Image Display",
    label: "صورة",
    icon: "Image",
    description: "عرض صورة المنتج",
    defaultConfig: {
      validation: { required: false }
    },
    configurable: ["label", "defaultValue"]
  }
]

// Layout Templates
export const LAYOUT_TEMPLATES = [
  {
    id: "single_column",
    name: "عمود واحد",
    description: "تخطيط بعمود واحد للجوال والديسكتوب",
    preview: "single-column-preview.svg"
  },
  {
    id: "two_column",
    name: "عمودين",
    description: "تخطيط بعمودين للديسكتوب وعمود واحد للجوال",
    preview: "two-column-preview.svg"
  },
  {
    id: "custom",
    name: "مخصص",
    description: "تخطيط مخصص قابل للتعديل",
    preview: "custom-layout-preview.svg"
  }
]
