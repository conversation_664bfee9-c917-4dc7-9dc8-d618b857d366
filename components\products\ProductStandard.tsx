"use client"

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { ShoppingCart, Star, Package } from "lucide-react"

interface ProductStandardProps {
  product: {
    id: string
    name: string
    description?: string
    category: string
    price?: number
    originalPrice?: number
    image?: string
    features?: string[]
  }
}

export function ProductStandard({ product }: ProductStandardProps) {
  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900">
      <div className="container mx-auto px-4 py-8">
        <Card className="max-w-4xl mx-auto bg-slate-800/50 border-slate-700/50 backdrop-blur-sm">
          <CardHeader>
            <div className="text-center space-y-4">
              <h1 className="text-2xl lg:text-3xl font-bold bg-gradient-to-r from-yellow-400 to-orange-500 bg-clip-text text-transparent">
                {product.name}
              </h1>
              {product.description && (
                <p className="text-slate-300">{product.description}</p>
              )}
              <Badge variant="secondary">{product.category}</Badge>
            </div>
          </CardHeader>
          
          <CardContent className="space-y-6">
            {product.image && (
              <div className="text-center">
                <img
                  src={product.image}
                  alt={product.name}
                  className="max-w-md mx-auto rounded-lg border border-slate-600"
                />
              </div>
            )}

            {product.features && (
              <div className="space-y-3">
                <h3 className="text-lg font-semibold text-white">المميزات</h3>
                <ul className="space-y-2">
                  {product.features.map((feature, index) => (
                    <li key={index} className="flex items-center gap-2 text-slate-300">
                      <Package className="h-4 w-4 text-green-400" />
                      {feature}
                    </li>
                  ))}
                </ul>
              </div>
            )}

            {product.price && (
              <div className="text-center space-y-2">
                <div className="text-3xl font-bold text-green-400">
                  {product.price} ج.س.
                </div>
                {product.originalPrice && product.originalPrice > product.price && (
                  <div className="text-lg text-slate-400 line-through">
                    {product.originalPrice} ج.س.
                  </div>
                )}
              </div>
            )}

            <div className="flex flex-col sm:flex-row gap-3 pt-6 border-t border-slate-700">
              <Button className="flex-1 bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700">
                <ShoppingCart className="h-4 w-4 ml-2" />
                إضافة إلى السلة
              </Button>
              <Button variant="outline" className="border-slate-600 text-slate-300">
                <Star className="h-4 w-4 ml-2" />
                إضافة للمفضلة
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
