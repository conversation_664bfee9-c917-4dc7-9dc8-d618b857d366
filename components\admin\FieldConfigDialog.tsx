"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON>alog<PERSON>eader, DialogTitle } from "@/components/ui/dialog"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Switch } from "@/components/ui/switch"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { 
  Save, 
  X, 
  Plus, 
  Trash2, 
  Settings, 
  Eye, 
  AlertCircle,
  Package
} from "lucide-react"
import { DynamicField, SelectOption, Package as PackageType } from "@/lib/types"

interface FieldConfigDialogProps {
  field: DynamicField
  onSave: (fieldId: string, updates: Partial<DynamicField>) => void
  onClose: () => void
}

export function FieldConfigDialog({ field, onSave, onClose }: FieldConfigDialogProps) {
  const [formData, setFormData] = useState<DynamicField>(field)
  const [activeTab, setActiveTab] = useState<"basic" | "options" | "validation">("basic")

  // Update form data when field changes
  useEffect(() => {
    setFormData(field)
  }, [field])

  // Handle basic field changes
  const handleBasicChange = (key: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [key]: value
    }))
  }

  // Handle validation changes
  const handleValidationChange = (key: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      validation: {
        ...prev.validation,
        [key]: value
      }
    }))
  }

  // Handle select options
  const handleAddOption = () => {
    if (formData.type === "select" || formData.type === "radio") {
      const newOption: SelectOption = {
        id: `option_${Date.now()}`,
        label: "خيار جديد",
        value: `option_${Date.now()}`,
        price: 0
      }
      
      setFormData(prev => ({
        ...prev,
        options: [...(prev.options || []), newOption]
      }))
    }
  }

  const handleUpdateOption = (optionId: string, updates: Partial<SelectOption>) => {
    if (formData.type === "select" || formData.type === "radio") {
      setFormData(prev => ({
        ...prev,
        options: prev.options?.map(option => 
          option.id === optionId ? { ...option, ...updates } : option
        ) || []
      }))
    }
  }

  const handleDeleteOption = (optionId: string) => {
    if (formData.type === "select" || formData.type === "radio") {
      setFormData(prev => ({
        ...prev,
        options: prev.options?.filter(option => option.id !== optionId) || []
      }))
    }
  }

  // Handle package options
  const handleAddPackage = () => {
    if (formData.type === "package_selector") {
      const newPackage: PackageType = {
        id: `package_${Date.now()}`,
        name: "حزمة جديدة",
        amount: "100",
        price: 50,
        description: "وصف الحزمة"
      }
      
      setFormData(prev => ({
        ...prev,
        packages: [...(prev.packages || []), newPackage]
      }))
    }
  }

  const handleUpdatePackage = (packageId: string, updates: Partial<PackageType>) => {
    if (formData.type === "package_selector") {
      setFormData(prev => ({
        ...prev,
        packages: prev.packages?.map(pkg => 
          pkg.id === packageId ? { ...pkg, ...updates } : pkg
        ) || []
      }))
    }
  }

  const handleDeletePackage = (packageId: string) => {
    if (formData.type === "package_selector") {
      setFormData(prev => ({
        ...prev,
        packages: prev.packages?.filter(pkg => pkg.id !== packageId) || []
      }))
    }
  }

  // Save changes
  const handleSave = () => {
    onSave(field.id, formData)
  }

  // Generate field name from label
  const generateFieldName = (label: string) => {
    return label
      .toLowerCase()
      .replace(/[^a-zA-Z0-9\u0600-\u06FF]/g, '_')
      .replace(/_+/g, '_')
      .replace(/^_|_$/g, '')
  }

  return (
    <Dialog open={true} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] bg-slate-800 border-slate-700 text-white overflow-hidden">
        <DialogHeader>
          <div className="flex items-center justify-between">
            <DialogTitle className="text-xl font-bold bg-gradient-to-r from-yellow-400 to-orange-500 bg-clip-text text-transparent">
              تكوين الحقل
            </DialogTitle>
            <Badge variant="secondary" className="text-xs">
              {field.type}
            </Badge>
          </div>
        </DialogHeader>

        <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as any)} className="space-y-4">
          <TabsList className="grid w-full grid-cols-3 bg-slate-700/50">
            <TabsTrigger value="basic" className="data-[state=active]:bg-slate-600">
              <Settings className="h-4 w-4 ml-2" />
              الأساسيات
            </TabsTrigger>
            {(field.type === "select" || field.type === "radio" || field.type === "package_selector") && (
              <TabsTrigger value="options" className="data-[state=active]:bg-slate-600">
                <Package className="h-4 w-4 ml-2" />
                {field.type === "package_selector" ? "الحزم" : "الخيارات"}
              </TabsTrigger>
            )}
            <TabsTrigger value="validation" className="data-[state=active]:bg-slate-600">
              <AlertCircle className="h-4 w-4 ml-2" />
              التحقق
            </TabsTrigger>
          </TabsList>

          {/* Basic Configuration */}
          <TabsContent value="basic" className="space-y-4 max-h-[60vh] overflow-y-auto">
            <Card className="bg-slate-700/50 border-slate-600">
              <CardHeader>
                <CardTitle className="text-white text-lg">المعلومات الأساسية</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="field-label" className="text-slate-300">
                      تسمية الحقل *
                    </Label>
                    <Input
                      id="field-label"
                      value={formData.label}
                      onChange={(e) => {
                        handleBasicChange("label", e.target.value)
                        handleBasicChange("name", generateFieldName(e.target.value))
                      }}
                      placeholder="مثال: معرف اللاعب"
                      className="bg-slate-600/50 border-slate-500 text-white"
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="field-name" className="text-slate-300">
                      اسم الحقل التقني
                    </Label>
                    <Input
                      id="field-name"
                      value={formData.name}
                      onChange={(e) => handleBasicChange("name", e.target.value)}
                      placeholder="player_id"
                      className="bg-slate-600/50 border-slate-500 text-white"
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="field-description" className="text-slate-300">
                    وصف الحقل
                  </Label>
                  <Textarea
                    id="field-description"
                    value={formData.description || ""}
                    onChange={(e) => handleBasicChange("description", e.target.value)}
                    placeholder="وصف مختصر للحقل..."
                    className="bg-slate-600/50 border-slate-500 text-white"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="field-placeholder" className="text-slate-300">
                    النص التوضيحي
                  </Label>
                  <Input
                    id="field-placeholder"
                    value={formData.placeholder || ""}
                    onChange={(e) => handleBasicChange("placeholder", e.target.value)}
                    placeholder="مثال: أدخل معرف اللاعب..."
                    className="bg-slate-600/50 border-slate-500 text-white"
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div className="space-y-1">
                    <Label className="text-slate-300">حقل مطلوب</Label>
                    <p className="text-sm text-slate-400">يجب على المستخدم ملء هذا الحقل</p>
                  </div>
                  <Switch
                    checked={formData.required}
                    onCheckedChange={(checked) => handleBasicChange("required", checked)}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div className="space-y-1">
                    <Label className="text-slate-300">مرئي</Label>
                    <p className="text-sm text-slate-400">إظهار الحقل للمستخدمين</p>
                  </div>
                  <Switch
                    checked={formData.visible}
                    onCheckedChange={(checked) => handleBasicChange("visible", checked)}
                  />
                </div>

                {/* Field-specific options */}
                {(field.type === "number" || field.type === "quantity_selector") && (
                  <div className="grid grid-cols-1 lg:grid-cols-3 gap-4">
                    <div className="space-y-2">
                      <Label className="text-slate-300">القيمة الدنيا</Label>
                      <Input
                        type="number"
                        value={formData.min || ""}
                        onChange={(e) => handleBasicChange("min", parseInt(e.target.value) || undefined)}
                        className="bg-slate-600/50 border-slate-500 text-white"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label className="text-slate-300">القيمة العليا</Label>
                      <Input
                        type="number"
                        value={formData.max || ""}
                        onChange={(e) => handleBasicChange("max", parseInt(e.target.value) || undefined)}
                        className="bg-slate-600/50 border-slate-500 text-white"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label className="text-slate-300">الخطوة</Label>
                      <Input
                        type="number"
                        value={formData.step || ""}
                        onChange={(e) => handleBasicChange("step", parseInt(e.target.value) || undefined)}
                        className="bg-slate-600/50 border-slate-500 text-white"
                      />
                    </div>
                  </div>
                )}

                {field.type === "heading" && (
                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label className="text-slate-300">مستوى العنوان</Label>
                      <Select
                        value={formData.level?.toString() || "2"}
                        onValueChange={(value) => handleBasicChange("level", parseInt(value))}
                      >
                        <SelectTrigger className="bg-slate-600/50 border-slate-500 text-white">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="1">H1 - عنوان رئيسي</SelectItem>
                          <SelectItem value="2">H2 - عنوان فرعي</SelectItem>
                          <SelectItem value="3">H3 - عنوان صغير</SelectItem>
                          <SelectItem value="4">H4 - عنوان أصغر</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    <div className="space-y-2">
                      <Label className="text-slate-300">المحاذاة</Label>
                      <Select
                        value={formData.alignment || "right"}
                        onValueChange={(value) => handleBasicChange("alignment", value)}
                      >
                        <SelectTrigger className="bg-slate-600/50 border-slate-500 text-white">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="right">يمين</SelectItem>
                          <SelectItem value="center">وسط</SelectItem>
                          <SelectItem value="left">يسار</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          {/* Options/Packages Configuration */}
          {(field.type === "select" || field.type === "radio" || field.type === "package_selector") && (
            <TabsContent value="options" className="space-y-4 max-h-[60vh] overflow-y-auto">
              <Card className="bg-slate-700/50 border-slate-600">
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <CardTitle className="text-white text-lg">
                      {field.type === "package_selector" ? "إدارة الحزم" : "إدارة الخيارات"}
                    </CardTitle>
                    <Button
                      onClick={field.type === "package_selector" ? handleAddPackage : handleAddOption}
                      size="sm"
                      className="bg-blue-500 hover:bg-blue-600"
                    >
                      <Plus className="h-4 w-4 ml-2" />
                      {field.type === "package_selector" ? "إضافة حزمة" : "إضافة خيار"}
                    </Button>
                  </div>
                </CardHeader>
                <CardContent className="space-y-3">
                  {field.type === "package_selector" ? (
                    // Package selector options
                    formData.packages?.map((pkg, index) => (
                      <Card key={pkg.id} className="bg-slate-600/50 border-slate-500">
                        <CardContent className="p-4">
                          <div className="grid grid-cols-1 lg:grid-cols-4 gap-3">
                            <Input
                              placeholder="اسم الحزمة"
                              value={pkg.name}
                              onChange={(e) => handleUpdatePackage(pkg.id, { name: e.target.value })}
                              className="bg-slate-500/50 border-slate-400 text-white"
                            />
                            <Input
                              placeholder="الكمية"
                              value={pkg.amount}
                              onChange={(e) => handleUpdatePackage(pkg.id, { amount: e.target.value })}
                              className="bg-slate-500/50 border-slate-400 text-white"
                            />
                            <Input
                              type="number"
                              placeholder="السعر"
                              value={pkg.price}
                              onChange={(e) => handleUpdatePackage(pkg.id, { price: parseFloat(e.target.value) || 0 })}
                              className="bg-slate-500/50 border-slate-400 text-white"
                            />
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleDeletePackage(pkg.id)}
                              className="text-red-400 hover:text-red-300 hover:bg-red-500/10"
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </div>
                        </CardContent>
                      </Card>
                    ))
                  ) : (
                    // Select/Radio options
                    formData.options?.map((option, index) => (
                      <Card key={option.id} className="bg-slate-600/50 border-slate-500">
                        <CardContent className="p-4">
                          <div className="grid grid-cols-1 lg:grid-cols-4 gap-3">
                            <Input
                              placeholder="تسمية الخيار"
                              value={option.label}
                              onChange={(e) => handleUpdateOption(option.id, { label: e.target.value })}
                              className="bg-slate-500/50 border-slate-400 text-white"
                            />
                            <Input
                              placeholder="القيمة"
                              value={option.value}
                              onChange={(e) => handleUpdateOption(option.id, { value: e.target.value })}
                              className="bg-slate-500/50 border-slate-400 text-white"
                            />
                            <Input
                              type="number"
                              placeholder="السعر (اختياري)"
                              value={option.price || ""}
                              onChange={(e) => handleUpdateOption(option.id, { price: parseFloat(e.target.value) || undefined })}
                              className="bg-slate-500/50 border-slate-400 text-white"
                            />
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleDeleteOption(option.id)}
                              className="text-red-400 hover:text-red-300 hover:bg-red-500/10"
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </div>
                        </CardContent>
                      </Card>
                    ))
                  )}
                  
                  {((field.type === "package_selector" && !formData.packages?.length) || 
                    ((field.type === "select" || field.type === "radio") && !formData.options?.length)) && (
                    <div className="text-center py-8 text-slate-400">
                      <Package className="h-12 w-12 mx-auto mb-3" />
                      <p>لا توجد {field.type === "package_selector" ? "حزم" : "خيارات"} بعد</p>
                    </div>
                  )}
                </CardContent>
              </Card>
            </TabsContent>
          )}

          {/* Validation Configuration */}
          <TabsContent value="validation" className="space-y-4 max-h-[60vh] overflow-y-auto">
            <Card className="bg-slate-700/50 border-slate-600">
              <CardHeader>
                <CardTitle className="text-white text-lg">قواعد التحقق</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {(field.type === "text" || field.type === "textarea") && (
                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label className="text-slate-300">الحد الأدنى للأحرف</Label>
                      <Input
                        type="number"
                        value={formData.validation?.min || ""}
                        onChange={(e) => handleValidationChange("min", parseInt(e.target.value) || undefined)}
                        className="bg-slate-600/50 border-slate-500 text-white"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label className="text-slate-300">الحد الأقصى للأحرف</Label>
                      <Input
                        type="number"
                        value={formData.validation?.max || ""}
                        onChange={(e) => handleValidationChange("max", parseInt(e.target.value) || undefined)}
                        className="bg-slate-600/50 border-slate-500 text-white"
                      />
                    </div>
                  </div>
                )}

                <div className="space-y-2">
                  <Label className="text-slate-300">نمط التحقق (Regex)</Label>
                  <Input
                    value={formData.validation?.pattern || ""}
                    onChange={(e) => handleValidationChange("pattern", e.target.value)}
                    placeholder="مثال: ^[0-9]+$ للأرقام فقط"
                    className="bg-slate-600/50 border-slate-500 text-white"
                  />
                </div>

                <div className="space-y-2">
                  <Label className="text-slate-300">رسالة خطأ مخصصة</Label>
                  <Input
                    value={formData.validation?.customMessage || ""}
                    onChange={(e) => handleValidationChange("customMessage", e.target.value)}
                    placeholder="رسالة الخطأ التي ستظهر للمستخدم"
                    className="bg-slate-600/50 border-slate-500 text-white"
                  />
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>

        {/* Footer Actions */}
        <div className="flex justify-end gap-3 pt-4 border-t border-slate-700">
          <Button
            variant="outline"
            onClick={onClose}
            className="border-slate-600 text-slate-300 hover:bg-slate-700"
          >
            <X className="h-4 w-4 ml-2" />
            إلغاء
          </Button>
          <Button
            onClick={handleSave}
            className="bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700"
          >
            <Save className="h-4 w-4 ml-2" />
            حفظ التغييرات
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  )
}
