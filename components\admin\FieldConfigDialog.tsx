"use client"

import React, { useState, useEffect } from "react"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Switch } from "@/components/ui/switch"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { 
  Save, 
  X, 
  Settings,
  Plus,
  Trash2,
  Package,
  AlertCircle
} from "lucide-react"
import { DynamicField, SelectOption, Package as PackageType } from "@/lib/types"

interface FieldConfigDialogProps {
  field: DynamicField
  onSave: (fieldId: string, updates: Partial<DynamicField>) => void
  onClose: () => void
}

export function FieldConfigDialog({ field, onSave, onClose }: FieldConfigDialogProps) {
  const [formData, setFormData] = useState<DynamicField>(field)
  const [activeTab, setActiveTab] = useState<"basic" | "options" | "validation">("basic")

  // Update form data when field changes
  useEffect(() => {
    setFormData(field)
  }, [field])

  // Handle basic field changes
  const handleBasicChange = (key: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [key]: value
    }))
  }

  // Handle validation changes
  const handleValidationChange = (key: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      validation: {
        ...prev.validation,
        [key]: value
      }
    }))
  }

  // Handle options for select/radio fields
  const handleAddOption = () => {
    if (formData.type === "select" || formData.type === "radio") {
      const newOption: SelectOption = {
        id: `option_${Date.now()}`,
        label: "خيار جديد",
        value: `option_${Date.now()}`,
        price: 0
      }
      
      setFormData(prev => ({
        ...prev,
        options: [...(prev.options || []), newOption]
      }))
    }
  }

  const handleUpdateOption = (optionId: string, updates: Partial<SelectOption>) => {
    setFormData(prev => ({
      ...prev,
      options: prev.options?.map(opt => 
        opt.id === optionId ? { ...opt, ...updates } : opt
      )
    }))
  }

  const handleDeleteOption = (optionId: string) => {
    setFormData(prev => ({
      ...prev,
      options: prev.options?.filter(opt => opt.id !== optionId)
    }))
  }

  // Handle packages for package_selector
  const handleAddPackage = () => {
    if (formData.type === "package_selector") {
      const newPackage: PackageType = {
        id: `pkg_${Date.now()}`,
        name: "حزمة جديدة",
        amount: "100",
        price: 50,
        description: ""
      }
      
      setFormData(prev => ({
        ...prev,
        packages: [...(prev.packages || []), newPackage]
      }))
    }
  }

  const handleUpdatePackage = (packageId: string, updates: Partial<PackageType>) => {
    setFormData(prev => ({
      ...prev,
      packages: prev.packages?.map(pkg => 
        pkg.id === packageId ? { ...pkg, ...updates } : pkg
      )
    }))
  }

  const handleDeletePackage = (packageId: string) => {
    setFormData(prev => ({
      ...prev,
      packages: prev.packages?.filter(pkg => pkg.id !== packageId)
    }))
  }

  // Save changes
  const handleSave = () => {
    onSave(field.id, formData)
    onClose()
  }

  // Generate field name from label
  const generateFieldName = (label: string) => {
    return label
      .toLowerCase()
      .replace(/[^a-zA-Z0-9\u0600-\u06FF]/g, '_')
      .replace(/_+/g, '_')
      .replace(/^_|_$/g, '')
  }

  return (
    <div className="fixed inset-0 z-50 bg-black/80 flex items-center justify-center p-2 sm:p-4">
      <div className="bg-slate-800 border border-slate-700 rounded-lg w-full max-w-4xl max-h-[95vh] sm:max-h-[90vh] overflow-hidden text-white relative">
        {/* Close Button */}
        <button
          onClick={onClose}
          className="absolute top-3 left-3 z-10 p-2 rounded-lg bg-slate-700/50 hover:bg-slate-600 transition-colors"
        >
          <X className="h-4 w-4 text-slate-300" />
        </button>

        {/* Header */}
        <div className="p-4 sm:p-6 pb-0">
          <div className="flex items-center justify-between">
            <h2 className="text-lg sm:text-xl font-bold bg-gradient-to-r from-yellow-400 to-orange-500 bg-clip-text text-transparent">
              تكوين الحقل
            </h2>
            <Badge variant="secondary" className="text-xs">
              {field.type}
            </Badge>
          </div>
        </div>

        {/* Content */}
        <div className="p-4 sm:p-6 pt-0 max-h-[85vh] sm:max-h-[80vh] overflow-y-auto">
          <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as any)} className="space-y-4">
            <TabsList className="grid w-full grid-cols-3 bg-slate-700/50 text-xs sm:text-sm">
              <TabsTrigger value="basic" className="data-[state=active]:bg-slate-600">
                <Settings className="h-3 w-3 sm:h-4 sm:w-4 ml-1 sm:ml-2" />
                <span className="hidden sm:inline">الأساسيات</span>
                <span className="sm:hidden">أساسي</span>
              </TabsTrigger>
              {(field.type === "select" || field.type === "radio" || field.type === "package_selector") && (
                <TabsTrigger value="options" className="data-[state=active]:bg-slate-600">
                  <Package className="h-3 w-3 sm:h-4 sm:w-4 ml-1 sm:ml-2" />
                  <span className="hidden sm:inline">{field.type === "package_selector" ? "الحزم" : "الخيارات"}</span>
                  <span className="sm:hidden">{field.type === "package_selector" ? "حزم" : "خيارات"}</span>
                </TabsTrigger>
              )}
              <TabsTrigger value="validation" className="data-[state=active]:bg-slate-600">
                <AlertCircle className="h-3 w-3 sm:h-4 sm:w-4 ml-1 sm:ml-2" />
                <span className="hidden sm:inline">التحقق</span>
                <span className="sm:hidden">تحقق</span>
              </TabsTrigger>
            </TabsList>

            {/* Basic Configuration */}
            <TabsContent value="basic" className="space-y-4">
              <Card className="bg-slate-700/50 border-slate-600">
                <CardHeader>
                  <CardTitle className="text-white text-base sm:text-lg">المعلومات الأساسية</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-1 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="field-label" className="text-slate-300">
                        تسمية الحقل *
                      </Label>
                      <Input
                        id="field-label"
                        value={formData.label}
                        onChange={(e) => {
                          handleBasicChange("label", e.target.value)
                          handleBasicChange("name", generateFieldName(e.target.value))
                        }}
                        placeholder="مثال: معرف اللاعب"
                        className="bg-slate-600/50 border-slate-500 text-white"
                      />
                    </div>
                    
                    <div className="space-y-2">
                      <Label htmlFor="field-name" className="text-slate-300">
                        اسم الحقل التقني
                      </Label>
                      <Input
                        id="field-name"
                        value={formData.name}
                        onChange={(e) => handleBasicChange("name", e.target.value)}
                        placeholder="player_id"
                        className="bg-slate-600/50 border-slate-500 text-white"
                      />
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="field-description" className="text-slate-300">
                      وصف الحقل
                    </Label>
                    <Textarea
                      id="field-description"
                      value={formData.description || ""}
                      onChange={(e) => handleBasicChange("description", e.target.value)}
                      placeholder="وصف مختصر للحقل..."
                      className="bg-slate-600/50 border-slate-500 text-white"
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="field-placeholder" className="text-slate-300">
                      النص التوضيحي
                    </Label>
                    <Input
                      id="field-placeholder"
                      value={formData.placeholder || ""}
                      onChange={(e) => handleBasicChange("placeholder", e.target.value)}
                      placeholder="مثال: أدخل معرف اللاعب..."
                      className="bg-slate-600/50 border-slate-500 text-white"
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="space-y-1">
                      <Label className="text-slate-300">حقل مطلوب</Label>
                      <p className="text-sm text-slate-400">يجب على المستخدم ملء هذا الحقل</p>
                    </div>
                    <Switch
                      checked={formData.required}
                      onCheckedChange={(checked) => handleBasicChange("required", checked)}
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="space-y-1">
                      <Label className="text-slate-300">مرئي</Label>
                      <p className="text-sm text-slate-400">إظهار الحقل للمستخدمين</p>
                    </div>
                    <Switch
                      checked={formData.visible}
                      onCheckedChange={(checked) => handleBasicChange("visible", checked)}
                    />
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            {/* Options Configuration */}
            {(field.type === "select" || field.type === "radio") && (
              <TabsContent value="options" className="space-y-4">
                <Card className="bg-slate-700/50 border-slate-600">
                  <CardHeader>
                    <div className="flex items-center justify-between">
                      <CardTitle className="text-white text-base sm:text-lg">خيارات الحقل</CardTitle>
                      <Button
                        onClick={handleAddOption}
                        size="sm"
                        className="bg-blue-500 hover:bg-blue-600"
                      >
                        <Plus className="h-4 w-4 ml-2" />
                        إضافة خيار
                      </Button>
                    </div>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    {formData.options?.map((option, index) => (
                      <div key={option.id} className="p-4 bg-slate-600/50 rounded-lg space-y-3">
                        <div className="flex items-center justify-between">
                          <h4 className="text-white font-medium">خيار {index + 1}</h4>
                          <Button
                            onClick={() => handleDeleteOption(option.id)}
                            size="sm"
                            variant="outline"
                            className="border-red-500 text-red-400 hover:bg-red-500/20"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>

                        <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
                          <div className="space-y-2">
                            <Label className="text-slate-300">تسمية الخيار</Label>
                            <Input
                              value={option.label}
                              onChange={(e) => handleUpdateOption(option.id, { label: e.target.value })}
                              placeholder="مثال: الخيار الأول"
                              className="bg-slate-700/50 border-slate-500 text-white"
                            />
                          </div>

                          <div className="space-y-2">
                            <Label className="text-slate-300">القيمة التقنية</Label>
                            <Input
                              value={option.value}
                              onChange={(e) => handleUpdateOption(option.id, { value: e.target.value })}
                              placeholder="option_1"
                              className="bg-slate-700/50 border-slate-500 text-white"
                            />
                          </div>
                        </div>

                        <div className="space-y-2">
                          <Label className="text-slate-300">سعر إضافي (اختياري)</Label>
                          <Input
                            type="number"
                            value={option.price || 0}
                            onChange={(e) => handleUpdateOption(option.id, { price: Number(e.target.value) })}
                            placeholder="0"
                            className="bg-slate-700/50 border-slate-500 text-white"
                          />
                        </div>
                      </div>
                    ))}

                    {(!formData.options || formData.options.length === 0) && (
                      <div className="text-center py-8 text-slate-400">
                        <Package className="h-12 w-12 mx-auto mb-3 opacity-50" />
                        <p>لا توجد خيارات بعد</p>
                        <p className="text-sm">انقر "إضافة خيار" لبدء إضافة الخيارات</p>
                      </div>
                    )}
                  </CardContent>
                </Card>
              </TabsContent>
            )}

            {/* Package Configuration */}
            {field.type === "package_selector" && (
              <TabsContent value="options" className="space-y-4">
                <Card className="bg-slate-700/50 border-slate-600">
                  <CardHeader>
                    <div className="flex items-center justify-between">
                      <CardTitle className="text-white text-base sm:text-lg">حزم المنتج</CardTitle>
                      <Button
                        onClick={handleAddPackage}
                        size="sm"
                        className="bg-blue-500 hover:bg-blue-600"
                      >
                        <Plus className="h-4 w-4 ml-2" />
                        إضافة حزمة
                      </Button>
                    </div>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    {formData.packages?.map((pkg, index) => (
                      <div key={pkg.id} className="p-4 bg-slate-600/50 rounded-lg space-y-3">
                        <div className="flex items-center justify-between">
                          <h4 className="text-white font-medium">حزمة {index + 1}</h4>
                          <Button
                            onClick={() => handleDeletePackage(pkg.id)}
                            size="sm"
                            variant="outline"
                            className="border-red-500 text-red-400 hover:bg-red-500/20"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>

                        <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
                          <div className="space-y-2">
                            <Label className="text-slate-300">اسم الحزمة</Label>
                            <Input
                              value={pkg.name}
                              onChange={(e) => handleUpdatePackage(pkg.id, { name: e.target.value })}
                              placeholder="مثال: حزمة الماس"
                              className="bg-slate-700/50 border-slate-500 text-white"
                            />
                          </div>

                          <div className="space-y-2">
                            <Label className="text-slate-300">الكمية</Label>
                            <Input
                              value={pkg.amount}
                              onChange={(e) => handleUpdatePackage(pkg.id, { amount: e.target.value })}
                              placeholder="100 ماسة"
                              className="bg-slate-700/50 border-slate-500 text-white"
                            />
                          </div>
                        </div>

                        <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
                          <div className="space-y-2">
                            <Label className="text-slate-300">السعر</Label>
                            <Input
                              type="number"
                              value={pkg.price}
                              onChange={(e) => handleUpdatePackage(pkg.id, { price: Number(e.target.value) })}
                              placeholder="50"
                              className="bg-slate-700/50 border-slate-500 text-white"
                            />
                          </div>

                          <div className="space-y-2">
                            <Label className="text-slate-300">السعر الأصلي (اختياري)</Label>
                            <Input
                              type="number"
                              value={pkg.originalPrice || ""}
                              onChange={(e) => handleUpdatePackage(pkg.id, { originalPrice: Number(e.target.value) || undefined })}
                              placeholder="70"
                              className="bg-slate-700/50 border-slate-500 text-white"
                            />
                          </div>
                        </div>

                        <div className="space-y-2">
                          <Label className="text-slate-300">وصف الحزمة</Label>
                          <Textarea
                            value={pkg.description || ""}
                            onChange={(e) => handleUpdatePackage(pkg.id, { description: e.target.value })}
                            placeholder="وصف مختصر للحزمة..."
                            className="bg-slate-700/50 border-slate-500 text-white"
                          />
                        </div>

                        <div className="flex items-center gap-4">
                          <label className="flex items-center gap-2">
                            <input
                              type="checkbox"
                              checked={pkg.popular || false}
                              onChange={(e) => handleUpdatePackage(pkg.id, { popular: e.target.checked })}
                              className="rounded border-slate-600 bg-slate-700"
                            />
                            <span className="text-slate-300 text-sm">حزمة شائعة</span>
                          </label>
                        </div>
                      </div>
                    ))}

                    {(!formData.packages || formData.packages.length === 0) && (
                      <div className="text-center py-8 text-slate-400">
                        <Package className="h-12 w-12 mx-auto mb-3 opacity-50" />
                        <p>لا توجد حزم بعد</p>
                        <p className="text-sm">انقر "إضافة حزمة" لبدء إضافة الحزم</p>
                      </div>
                    )}
                  </CardContent>
                </Card>
              </TabsContent>
            )}

            {/* Validation Configuration */}
            <TabsContent value="validation" className="space-y-4">
              <Card className="bg-slate-700/50 border-slate-600">
                <CardHeader>
                  <CardTitle className="text-white text-base sm:text-lg">قواعد التحقق</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  {(field.type === "text" || field.type === "textarea" || field.type === "number") && (
                    <>
                      <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                        <div className="space-y-2">
                          <Label className="text-slate-300">الحد الأدنى</Label>
                          <Input
                            type="number"
                            value={formData.validation?.min || ""}
                            onChange={(e) => handleValidationChange("min", Number(e.target.value) || undefined)}
                            placeholder={field.type === "number" ? "0" : "5 أحرف"}
                            className="bg-slate-600/50 border-slate-500 text-white"
                          />
                        </div>

                        <div className="space-y-2">
                          <Label className="text-slate-300">الحد الأقصى</Label>
                          <Input
                            type="number"
                            value={formData.validation?.max || ""}
                            onChange={(e) => handleValidationChange("max", Number(e.target.value) || undefined)}
                            placeholder={field.type === "number" ? "999999" : "100 حرف"}
                            className="bg-slate-600/50 border-slate-500 text-white"
                          />
                        </div>
                      </div>

                      {field.type === "text" && (
                        <div className="space-y-2">
                          <Label className="text-slate-300">نمط التحقق (Regex)</Label>
                          <Input
                            value={formData.validation?.pattern || ""}
                            onChange={(e) => handleValidationChange("pattern", e.target.value)}
                            placeholder="^[0-9]+$ (أرقام فقط)"
                            className="bg-slate-600/50 border-slate-500 text-white"
                          />
                        </div>
                      )}
                    </>
                  )}

                  {field.type === "image" && (
                    <>
                      <div className="space-y-2">
                        <Label className="text-slate-300">الحد الأقصى لحجم الملف (ميجابايت)</Label>
                        <Input
                          type="number"
                          value={formData.maxSize || 5}
                          onChange={(e) => handleBasicChange("maxSize", Number(e.target.value))}
                          placeholder="5"
                          className="bg-slate-600/50 border-slate-500 text-white"
                        />
                      </div>

                      <div className="flex items-center justify-between">
                        <div className="space-y-1">
                          <Label className="text-slate-300">السماح برفع عدة صور</Label>
                          <p className="text-sm text-slate-400">يمكن للمستخدم رفع أكثر من صورة</p>
                        </div>
                        <Switch
                          checked={formData.multiple || false}
                          onCheckedChange={(checked) => handleBasicChange("multiple", checked)}
                        />
                      </div>
                    </>
                  )}

                  <div className="space-y-2">
                    <Label className="text-slate-300">رسالة خطأ مخصصة</Label>
                    <Textarea
                      value={formData.validation?.customMessage || ""}
                      onChange={(e) => handleValidationChange("customMessage", e.target.value)}
                      placeholder="رسالة الخطأ التي ستظهر للمستخدم..."
                      className="bg-slate-600/50 border-slate-500 text-white"
                    />
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>

        {/* Footer Actions */}
        <div className="flex flex-col sm:flex-row justify-end gap-3 p-4 sm:p-6 pt-4 border-t border-slate-700">
          <Button
            variant="outline"
            onClick={onClose}
            className="border-slate-600 text-slate-300 hover:bg-slate-700 w-full sm:w-auto"
          >
            <X className="h-4 w-4 ml-2" />
            إلغاء
          </Button>
          <Button
            onClick={handleSave}
            className="bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 w-full sm:w-auto"
          >
            <Save className="h-4 w-4 ml-2" />
            حفظ التغييرات
          </Button>
        </div>
      </div>
    </div>
  )
}
