"use client"

import { useState, use<PERSON>allback } from "react"
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import {
  Plus,
  GripVertical,
  Edit,
  Trash2,
  Type,
  Hash,
  Mail,
  FileText,
  List,
  Package,
  Image,
  CheckSquare,
  DollarSign,
  Minus,
  Heading,
  Lock,
  Phone,
  Layers,
  UserCheck,
  FolderOpen
} from "lucide-react"
import { DynamicField, FieldType } from "@/lib/types"
import { FieldTypeSelector } from "./FieldTypeSelector"
import { FieldConfigDialog } from "./FieldConfigDialog"

interface FieldEditorProps {
  fields: DynamicField[]
  onFieldAdd: (field: DynamicField) => void
  onFieldUpdate: (fieldId: string, updates: Partial<DynamicField>) => void
  onFieldDelete: (fieldId: string) => void
  onFieldReorder: (dragIndex: number, hoverIndex: number) => void
}

// Field type icons mapping
const fieldTypeIcons: Record<FieldType, React.ReactNode> = {
  text: <Type className="h-4 w-4" />,
  email: <Mail className="h-4 w-4" />,
  password: <Lock className="h-4 w-4" />,
  phone: <Phone className="h-4 w-4" />,
  number: <Hash className="h-4 w-4" />,
  textarea: <FileText className="h-4 w-4" />,
  select: <List className="h-4 w-4" />,
  package_selector: <Package className="h-4 w-4" />,
  grouped_packages: <Layers className="h-4 w-4" />,
  account_type_selector: <UserCheck className="h-4 w-4" />,
  credentials_group: <FolderOpen className="h-4 w-4" />,
  image: <Image className="h-4 w-4" />,
  checkbox: <CheckSquare className="h-4 w-4" />,
  radio: <List className="h-4 w-4" />,
  quantity_selector: <Hash className="h-4 w-4" />,
  price_display: <DollarSign className="h-4 w-4" />,
  divider: <Minus className="h-4 w-4" />,
  heading: <Heading className="h-4 w-4" />
}

// Field type labels in Arabic
const fieldTypeLabels: Record<FieldType, string> = {
  text: "نص",
  email: "بريد إلكتروني", 
  number: "رقم",
  textarea: "نص طويل",
  select: "قائمة منسدلة",
  package_selector: "محدد الحزم",
  image: "صورة",
  checkbox: "مربع اختيار",
  radio: "اختيار واحد",
  quantity_selector: "محدد الكمية",
  price_display: "عرض السعر",
  divider: "فاصل",
  heading: "عنوان"
}

export function FieldEditor({ 
  fields, 
  onFieldAdd, 
  onFieldUpdate, 
  onFieldDelete, 
  onFieldReorder 
}: FieldEditorProps) {
  const [showFieldSelector, setShowFieldSelector] = useState(false)
  const [editingField, setEditingField] = useState<DynamicField | null>(null)
  const [draggedField, setDraggedField] = useState<string | null>(null)

  // Handle field type selection
  const handleFieldTypeSelect = useCallback((type: FieldType) => {
    const baseField = {
      id: `field_${Date.now()}`,
      type,
      label: `حقل ${fieldTypeLabels[type]}`,
      name: `field_${type}_${Date.now()}`,
      required: false,
      order: fields.length,
      visible: true,
      description: "",
      placeholder: ""
    }

    let newField: DynamicField

    // Add type-specific properties
    switch (type) {
      case "select":
      case "radio":
        newField = {
          ...baseField,
          type,
          options: [
            {
              id: `option_1_${Date.now()}`,
              label: "الخيار الأول",
              value: "option_1",
              price: 0
            },
            {
              id: `option_2_${Date.now()}`,
              label: "الخيار الثاني",
              value: "option_2",
              price: 0
            }
          ]
        } as DynamicField
        break

      case "package_selector":
        newField = {
          ...baseField,
          type,
          packages: [
            {
              id: `pkg_1_${Date.now()}`,
              name: "الحزمة الأساسية",
              amount: "100",
              price: 25,
              description: "حزمة مناسبة للمبتدئين"
            },
            {
              id: `pkg_2_${Date.now()}`,
              name: "الحزمة المتقدمة",
              amount: "500",
              price: 100,
              originalPrice: 120,
              popular: true,
              description: "الحزمة الأكثر شعبية"
            }
          ],
          displayStyle: "cards",
          allowMultiple: false
        } as DynamicField
        break

      case "image":
        newField = {
          ...baseField,
          type,
          maxSize: 5,
          allowedTypes: ['image/jpeg', 'image/png', 'image/webp'],
          multiple: false
        } as DynamicField
        break

      case "heading":
        newField = {
          ...baseField,
          type,
          level: 2,
          alignment: "center",
          color: "#ffffff"
        } as DynamicField
        break

      case "divider":
        newField = {
          ...baseField,
          type,
          style: "line",
          height: 1
        } as DynamicField
        break

      case "price_display":
        newField = {
          ...baseField,
          type,
          currency: "ج.س.",
          showOriginalPrice: true,
          showDiscount: true
        } as DynamicField
        break

      case "checkbox":
        newField = {
          ...baseField,
          type,
          defaultChecked: false
        } as DynamicField
        break

      default:
        newField = baseField as DynamicField
    }

    onFieldAdd(newField)
    setShowFieldSelector(false)
  }, [fields.length, onFieldAdd])

  // Handle field editing
  const handleFieldEdit = useCallback((field: DynamicField) => {
    setEditingField(field)
  }, [])

  const handleFieldSave = useCallback((fieldId: string, updates: Partial<DynamicField>) => {
    onFieldUpdate(fieldId, updates)
    setEditingField(null)
  }, [onFieldUpdate])

  // Handle drag and drop
  const handleDragStart = useCallback((fieldId: string) => {
    setDraggedField(fieldId)
  }, [])

  const handleDragOver = useCallback((e: React.DragEvent, targetFieldId: string) => {
    e.preventDefault()
    if (!draggedField || draggedField === targetFieldId) return

    const dragIndex = fields.findIndex(f => f.id === draggedField)
    const hoverIndex = fields.findIndex(f => f.id === targetFieldId)
    
    if (dragIndex !== -1 && hoverIndex !== -1) {
      onFieldReorder(dragIndex, hoverIndex)
    }
  }, [draggedField, fields, onFieldReorder])

  const handleDragEnd = useCallback(() => {
    setDraggedField(null)
  }, [])

  return (
    <div className="space-y-6">
      {/* Header */}
      <Card className="bg-slate-800/50 border-slate-700/50 backdrop-blur-sm">
        <CardHeader>
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
            <CardTitle className="text-white flex items-center gap-2">
              <Plus className="h-5 w-5" />
              إدارة الحقول ({fields.length})
            </CardTitle>
            <Button
              onClick={() => setShowFieldSelector(true)}
              className="bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 w-full sm:w-auto"
            >
              <Plus className="h-4 w-4 ml-2" />
              إضافة حقل جديد
            </Button>
          </div>
        </CardHeader>
      </Card>

      {/* Fields List */}
      {fields.length === 0 ? (
        <Card className="bg-slate-800/50 border-slate-700/50 backdrop-blur-sm">
          <CardContent className="p-8 text-center">
            <Package className="h-16 w-16 text-slate-400 mx-auto mb-4" />
            <h3 className="text-xl font-bold text-white mb-2">لا توجد حقول بعد</h3>
            <p className="text-slate-300 mb-4">
              ابدأ بإضافة حقول لبناء نموذج المنتج الخاص بك
            </p>
            <Button
              onClick={() => setShowFieldSelector(true)}
              className="bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700"
            >
              <Plus className="h-4 w-4 ml-2" />
              إضافة أول حقل
            </Button>
          </CardContent>
        </Card>
      ) : (
        <div className="space-y-3">
          {fields.map((field, index) => (
            <Card
              key={field.id}
              className={`bg-slate-800/50 border-slate-700/50 backdrop-blur-sm transition-all duration-200 ${
                draggedField === field.id ? "opacity-50 scale-95" : "hover:bg-slate-700/30"
              }`}
              draggable
              onDragStart={() => handleDragStart(field.id)}
              onDragOver={(e) => handleDragOver(e, field.id)}
              onDragEnd={handleDragEnd}
            >
              <CardContent className="p-4">
                <div className="flex items-center gap-3">
                  {/* Drag Handle */}
                  <div className="cursor-grab active:cursor-grabbing text-slate-400 hover:text-slate-300">
                    <GripVertical className="h-5 w-5" />
                  </div>

                  {/* Field Icon */}
                  <div className="p-2 bg-slate-700/50 rounded-lg text-slate-300">
                    {fieldTypeIcons[field.type]}
                  </div>

                  {/* Field Info */}
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center gap-2 mb-1">
                      <h4 className="font-medium text-white truncate">{field.label}</h4>
                      <Badge variant="secondary" className="text-xs">
                        {fieldTypeLabels[field.type]}
                      </Badge>
                      {field.required && (
                        <Badge variant="destructive" className="text-xs">
                          مطلوب
                        </Badge>
                      )}
                    </div>
                    <p className="text-sm text-slate-400 truncate">
                      {field.description || field.placeholder || `الحقل رقم ${index + 1}`}
                    </p>
                  </div>

                  {/* Actions */}
                  <div className="flex items-center gap-2">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleFieldEdit(field)}
                      className="text-slate-400 hover:text-white hover:bg-slate-700"
                    >
                      <Edit className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => onFieldDelete(field.id)}
                      className="text-slate-400 hover:text-red-400 hover:bg-red-500/10"
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      {/* Field Type Selector Dialog */}
      {showFieldSelector && (
        <FieldTypeSelector
          onSelect={handleFieldTypeSelect}
          onClose={() => setShowFieldSelector(false)}
        />
      )}

      {/* Field Configuration Dialog */}
      {editingField && (
        <FieldConfigDialog
          field={editingField}
          onSave={handleFieldSave}
          onClose={() => setEditingField(null)}
        />
      )}
    </div>
  )
}
