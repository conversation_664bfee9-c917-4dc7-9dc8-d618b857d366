import { Home, Store, User, Phone, Info, Wallet, Settings } from "lucide-react"
import { MenuItem } from "@/lib/types"

export const menuItems: MenuItem[] = [
  { icon: <Home className="h-5 w-5" />, label: "الرئيسية", href: "/" },
  { icon: <Store className="h-5 w-5" />, label: "المتجر", href: "/shop" },
  { icon: <Wallet className="h-5 w-5" />, label: "المحفظة", href: "/wallet" },
  { icon: <User className="h-5 w-5" />, label: "حسابي", href: "/profile" },
  { icon: <Settings className="h-5 w-5" />, label: "لوحة الإدارة", href: "/admin" },
  { icon: <Phone className="h-5 w-5" />, label: "اتصل بنا", href: "#contact" },
  { icon: <Info className="h-5 w-5" />, label: "من نحن", href: "#about" },
]
