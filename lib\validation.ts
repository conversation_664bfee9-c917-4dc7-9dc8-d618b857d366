import { DynamicField, FieldValidation } from "./types"

export interface ValidationResult {
  isValid: boolean
  error?: string
}

export function validateField(field: DynamicField, value: any): ValidationResult {
  // Skip validation if field is not required and value is empty
  if (!field.required && (!value || value === "")) {
    return { isValid: true }
  }

  // Required field validation
  if (field.required && (!value || value === "")) {
    return {
      isValid: false,
      error: field.validation?.customMessage || `${field.label} مطلوب`
    }
  }

  // Type-specific validation
  switch (field.type) {
    case "email":
      return validateEmail(value, field.validation)
    
    case "number":
      return validateNumber(value, field.validation)
    
    case "text":
    case "textarea":
      return validateText(value, field.validation)
    
    case "select":
    case "radio":
      return validateSelection(value, field)
    
    case "image":
      return validateImage(value, field)
    
    case "checkbox":
      if (field.required && !value) {
        return {
          isValid: false,
          error: field.validation?.customMessage || `يجب الموافقة على ${field.label}`
        }
      }
      return { isValid: true }
    
    default:
      return { isValid: true }
  }
}

function validateEmail(value: string, validation?: FieldValidation): ValidationResult {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  
  if (!emailRegex.test(value)) {
    return {
      isValid: false,
      error: validation?.customMessage || "يرجى إدخال بريد إلكتروني صحيح"
    }
  }
  
  return { isValid: true }
}

function validateNumber(value: any, validation?: FieldValidation): ValidationResult {
  const numValue = Number(value)
  
  if (isNaN(numValue)) {
    return {
      isValid: false,
      error: validation?.customMessage || "يرجى إدخال رقم صحيح"
    }
  }
  
  if (validation?.min !== undefined && numValue < validation.min) {
    return {
      isValid: false,
      error: validation?.customMessage || `القيمة يجب أن تكون أكبر من أو تساوي ${validation.min}`
    }
  }
  
  if (validation?.max !== undefined && numValue > validation.max) {
    return {
      isValid: false,
      error: validation?.customMessage || `القيمة يجب أن تكون أقل من أو تساوي ${validation.max}`
    }
  }
  
  return { isValid: true }
}

function validateText(value: string, validation?: FieldValidation): ValidationResult {
  if (validation?.min !== undefined && value.length < validation.min) {
    return {
      isValid: false,
      error: validation?.customMessage || `يجب أن يكون النص ${validation.min} أحرف على الأقل`
    }
  }
  
  if (validation?.max !== undefined && value.length > validation.max) {
    return {
      isValid: false,
      error: validation?.customMessage || `يجب أن يكون النص ${validation.max} حرف كحد أقصى`
    }
  }
  
  if (validation?.pattern) {
    try {
      const regex = new RegExp(validation.pattern)
      if (!regex.test(value)) {
        return {
          isValid: false,
          error: validation?.customMessage || "تنسيق النص غير صحيح"
        }
      }
    } catch (error) {
      console.warn("Invalid regex pattern:", validation.pattern)
    }
  }
  
  return { isValid: true }
}

function validateSelection(value: any, field: DynamicField): ValidationResult {
  if (!field.options || field.options.length === 0) {
    return { isValid: true }
  }
  
  const validOptions = field.options.map(opt => opt.value)
  
  if (!validOptions.includes(value)) {
    return {
      isValid: false,
      error: field.validation?.customMessage || "يرجى اختيار خيار صحيح"
    }
  }
  
  return { isValid: true }
}

function validateImage(value: any, field: DynamicField): ValidationResult {
  if (!value) {
    return { isValid: true }
  }
  
  // For now, we'll assume the value is a valid image URL or file
  // In a real implementation, you'd validate file size, type, etc.
  if (Array.isArray(value)) {
    if (!field.multiple) {
      return {
        isValid: false,
        error: field.validation?.customMessage || "هذا الحقل لا يدعم رفع عدة صور"
      }
    }
  }
  
  return { isValid: true }
}

// Validate entire form
export function validateForm(fields: DynamicField[], formData: Record<string, any>): {
  isValid: boolean
  errors: Record<string, string>
} {
  const errors: Record<string, string> = {}
  
  for (const field of fields) {
    if (!field.visible) continue
    
    const value = formData[field.name]
    const result = validateField(field, value)
    
    if (!result.isValid && result.error) {
      errors[field.name] = result.error
    }
  }
  
  return {
    isValid: Object.keys(errors).length === 0,
    errors
  }
}

// Real-time validation hook
export function useFieldValidation(field: DynamicField, value: any) {
  const result = validateField(field, value)
  return result
}
