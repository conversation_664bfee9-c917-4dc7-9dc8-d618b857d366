"use client"

import { useState, useEffect } from "react"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Switch } from "@/components/ui/switch"
import { AppHeader } from "@/components/layout/AppHeader"
import { SideMenu } from "@/components/layout/SideMenu"
import { NewsTicket } from "@/components/shared/NewsTicket"
import { 
  getCheckoutConfig, 
  setCheckoutConfig, 
  addBankAccount, 
  updateBankAccount, 
  deleteBankAccount,
  addRechargeOption,
  updateRechargeOption,
  deleteRechargeOption
} from "@/lib/utils/localStorage"
import { BankAccount, RechargeOption, Currency } from "@/lib/types"
import { cn } from "@/lib/utils"
import { 
  <PERSON><PERSON><PERSON>, 
  Building2, 
  Plus, 
  Trash2, 
  Edit, 
  Save,
  Wallet,
  FileText
} from "lucide-react"

export function CheckoutConfigPage() {
  const [isMenuOpen, setIsMenuOpen] = useState(false)
  const [config, setConfig] = useState(getCheckoutConfig())
  const [editingBank, setEditingBank] = useState<string | null>(null)
  const [editingAmount, setEditingAmount] = useState<string | null>(null)
  const [newBankForm, setNewBankForm] = useState({
    name: "",
    accountNumber: "",
    logoUrl: ""
  })
  const [newAmountForm, setNewAmountForm] = useState({
    amount: "",
    currency: "SDG" as Currency
  })

  // Refresh config when component mounts
  useEffect(() => {
    setConfig(getCheckoutConfig())
  }, [])

  const handleSaveConfig = () => {
    setCheckoutConfig(config)
    alert("تم حفظ الإعدادات بنجاح")
  }

  // Bank Account Management
  const handleAddBank = () => {
    if (!newBankForm.name || !newBankForm.accountNumber) return

    const newBank = addBankAccount({
      name: newBankForm.name,
      accountNumber: newBankForm.accountNumber,
      logoUrl: newBankForm.logoUrl || undefined,
      isActive: true
    })

    setConfig(prev => ({
      ...prev,
      bankAccounts: [...prev.bankAccounts, newBank]
    }))

    setNewBankForm({ name: "", accountNumber: "", logoUrl: "" })
  }

  const handleUpdateBank = (bankId: string, updates: Partial<BankAccount>) => {
    updateBankAccount(bankId, updates)
    setConfig(prev => ({
      ...prev,
      bankAccounts: prev.bankAccounts.map(bank => 
        bank.id === bankId ? { ...bank, ...updates } : bank
      )
    }))
    setEditingBank(null)
  }

  const handleDeleteBank = (bankId: string) => {
    if (confirm("هل أنت متأكد من حذف هذا البنك؟")) {
      deleteBankAccount(bankId)
      setConfig(prev => ({
        ...prev,
        bankAccounts: prev.bankAccounts.filter(bank => bank.id !== bankId)
      }))
    }
  }

  // Recharge Amount Management
  const handleAddAmount = () => {
    const amount = parseInt(newAmountForm.amount)
    if (!amount || amount <= 0) return

    const newOption = addRechargeOption({
      amount,
      currency: newAmountForm.currency,
      isActive: true
    })

    setConfig(prev => ({
      ...prev,
      rechargeOptions: [...prev.rechargeOptions, newOption]
    }))

    setNewAmountForm({ amount: "", currency: "SDG" })
  }

  const handleUpdateAmount = (optionId: string, updates: Partial<RechargeOption>) => {
    updateRechargeOption(optionId, updates)
    setConfig(prev => ({
      ...prev,
      rechargeOptions: prev.rechargeOptions.map(option => 
        option.id === optionId ? { ...option, ...updates } : option
      )
    }))
    setEditingAmount(null)
  }

  const handleDeleteAmount = (optionId: string) => {
    if (confirm("هل أنت متأكد من حذف هذا المبلغ؟")) {
      deleteRechargeOption(optionId)
      setConfig(prev => ({
        ...prev,
        rechargeOptions: prev.rechargeOptions.filter(option => option.id !== optionId)
      }))
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900 text-white relative overflow-hidden">
      {/* Background Effects */}
      <div className="absolute inset-0 bg-[radial-gradient(ellipse_at_top,_var(--tw-gradient-stops))] from-blue-400/20 via-transparent to-transparent" />
      <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-blue-400/10 rounded-full blur-3xl" />
      <div className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-purple-500/10 rounded-full blur-3xl" />

      <AppHeader onMenuOpen={() => setIsMenuOpen(true)} />
      <NewsTicket />
      <SideMenu isOpen={isMenuOpen} onClose={() => setIsMenuOpen(false)} />

      {/* Main Content */}
      <main className="relative z-10 p-4 lg:p-8 space-y-6 lg:space-y-8 pb-32 pt-32 lg:pt-36 max-w-7xl mx-auto">
        {/* Page Header */}
        <div className="text-center space-y-4">
          <div className="flex items-center justify-center gap-4 mb-4">
            <div className="p-3 bg-gradient-to-r from-blue-400 to-purple-500 rounded-2xl shadow-lg">
              <Settings className="h-8 w-8 text-white" />
            </div>
          </div>
          <h1 className="text-3xl lg:text-4xl font-bold bg-gradient-to-r from-blue-400 to-purple-500 bg-clip-text text-transparent">
            إعدادات الشحن
          </h1>
          <p className="text-slate-300 text-base lg:text-lg max-w-md mx-auto">
            إدارة البنوك والمبالغ المحددة مسبقاً لنظام الشحن
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Bank Accounts Management */}
          <Card className="bg-slate-800/50 backdrop-blur-xl border-slate-700/50 shadow-2xl">
            <CardHeader>
              <CardTitle className="flex items-center gap-3 text-xl text-white">
                <Building2 className="h-6 w-6 text-blue-400" />
                إدارة البنوك
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Add New Bank */}
              <div className="space-y-3 p-4 bg-slate-700/30 rounded-lg">
                <h4 className="font-medium text-white">إضافة بنك جديد</h4>
                <div className="grid grid-cols-1 gap-3">
                  <Input
                    placeholder="اسم البنك"
                    value={newBankForm.name}
                    onChange={(e) => setNewBankForm(prev => ({ ...prev, name: e.target.value }))}
                    className="bg-slate-600/50 border-slate-500 text-white"
                  />
                  <Input
                    placeholder="رقم الحساب"
                    value={newBankForm.accountNumber}
                    onChange={(e) => setNewBankForm(prev => ({ ...prev, accountNumber: e.target.value }))}
                    className="bg-slate-600/50 border-slate-500 text-white"
                  />
                  <Input
                    placeholder="رابط الشعار (اختياري)"
                    value={newBankForm.logoUrl}
                    onChange={(e) => setNewBankForm(prev => ({ ...prev, logoUrl: e.target.value }))}
                    className="bg-slate-600/50 border-slate-500 text-white"
                  />
                  <Button onClick={handleAddBank} className="bg-blue-500 hover:bg-blue-600">
                    <Plus className="h-4 w-4 ml-2" />
                    إضافة
                  </Button>
                </div>
              </div>

              {/* Existing Banks */}
              <div className="space-y-3">
                {config.bankAccounts.map((bank) => (
                  <div key={bank.id} className="p-4 bg-slate-700/50 rounded-lg">
                    {editingBank === bank.id ? (
                      <div className="space-y-3">
                        <Input
                          defaultValue={bank.name}
                          onBlur={(e) => handleUpdateBank(bank.id, { name: e.target.value })}
                          className="bg-slate-600/50 border-slate-500 text-white"
                        />
                        <Input
                          defaultValue={bank.accountNumber}
                          onBlur={(e) => handleUpdateBank(bank.id, { accountNumber: e.target.value })}
                          className="bg-slate-600/50 border-slate-500 text-white"
                        />
                        <Button onClick={() => setEditingBank(null)} size="sm">
                          <Save className="h-4 w-4 ml-2" />
                          حفظ
                        </Button>
                      </div>
                    ) : (
                      <div className="flex items-center justify-between">
                        <div>
                          <h5 className="font-medium text-white">{bank.name}</h5>
                          <p className="text-slate-400 text-sm font-mono">{bank.accountNumber}</p>
                        </div>
                        <div className="flex items-center gap-2">
                          <Switch
                            checked={bank.isActive}
                            onCheckedChange={(checked) => handleUpdateBank(bank.id, { isActive: checked })}
                          />
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => setEditingBank(bank.id)}
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleDeleteBank(bank.id)}
                            className="text-red-400 hover:text-red-300"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Recharge Amounts Management */}
          <Card className="bg-slate-800/50 backdrop-blur-xl border-slate-700/50 shadow-2xl">
            <CardHeader>
              <CardTitle className="flex items-center gap-3 text-xl text-white">
                <Wallet className="h-6 w-6 text-purple-400" />
                إدارة المبالغ
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Add New Amount */}
              <div className="space-y-3 p-4 bg-slate-700/30 rounded-lg">
                <h4 className="font-medium text-white">إضافة مبلغ جديد</h4>
                <div className="grid grid-cols-2 gap-3">
                  <Input
                    type="number"
                    placeholder="المبلغ"
                    value={newAmountForm.amount}
                    onChange={(e) => setNewAmountForm(prev => ({ ...prev, amount: e.target.value }))}
                    className="bg-slate-600/50 border-slate-500 text-white"
                  />
                  <select
                    value={newAmountForm.currency}
                    onChange={(e) => setNewAmountForm(prev => ({ ...prev, currency: e.target.value as Currency }))}
                    className="bg-slate-600/50 border border-slate-500 rounded-md px-3 py-2 text-white"
                  >
                    <option value="SDG">SDG</option>
                    <option value="EGP">EGP</option>
                  </select>
                </div>
                <Button onClick={handleAddAmount} className="bg-purple-500 hover:bg-purple-600 w-full">
                  <Plus className="h-4 w-4 ml-2" />
                  إضافة
                </Button>
              </div>

              {/* Existing Amounts */}
              <div className="space-y-3">
                {config.rechargeOptions.map((option) => (
                  <div key={option.id} className="p-4 bg-slate-700/50 rounded-lg">
                    <div className="flex items-center justify-between">
                      <div>
                        <span className="font-medium text-white">
                          {option.amount.toLocaleString()} {option.currency}
                        </span>
                      </div>
                      <div className="flex items-center gap-2">
                        <Switch
                          checked={option.isActive}
                          onCheckedChange={(checked) => handleUpdateAmount(option.id, { isActive: checked })}
                        />
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleDeleteAmount(option.id)}
                          className="text-red-400 hover:text-red-300"
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Save Button */}
        <div className="text-center">
          <Button
            onClick={handleSaveConfig}
            className="px-8 py-3 text-lg font-semibold bg-gradient-to-r from-green-500 to-green-600 text-white hover:from-green-600 hover:to-green-700 hover:scale-105 shadow-lg hover:shadow-green-500/25 transition-all duration-300"
          >
            <Save className="h-5 w-5 ml-2" />
            حفظ جميع الإعدادات
          </Button>
        </div>
      </main>
    </div>
  )
}
