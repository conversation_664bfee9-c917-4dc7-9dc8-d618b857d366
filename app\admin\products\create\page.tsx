"use client"

import { ProductForm } from "@/components/admin/ProductForm"
import { ProductTemplate } from "@/lib/types"

export default function CreateProductPage() {
  const handleSave = (template: ProductTemplate) => {
    // ## Save to Supabase - will be implemented with backend integration
    console.log("Saving template:", template)
    
    // For now, just show success message
    alert("تم حفظ القالب بنجاح!")
    
    // Redirect back to admin dashboard
    window.location.href = "/admin"
  }

  const handlePreview = (template: ProductTemplate) => {
    // ## Open preview in new tab or modal
    console.log("Previewing template:", template)
    alert("معاينة القالب - سيتم تطوير هذه الميزة قريباً")
  }

  return (
    <ProductForm
      onSave={handleSave}
      onPreview={handlePreview}
      isLoading={false}
    />
  )
}
