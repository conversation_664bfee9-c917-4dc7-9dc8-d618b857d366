"use client"

import { useRouter } from "next/navigation"
import { AdminLayout } from "@/components/admin/AdminLayout"
import { ProductBuilder } from "@/components/admin/ProductBuilder"
import { DynamicProduct } from "@/lib/types"
import { ArrowLeft } from "lucide-react"
import { Button } from "@/components/ui/button"

export default function CreateProductPage() {
  const router = useRouter()

  const handleSave = (product: DynamicProduct) => {
    // TODO: Save to Supabase
    console.log("Saving product:", product)
    
    // For now, just show success and redirect
    alert("تم إنشاء المنتج بنجاح!")
    router.push("/admin/products")
  }

  const handleCancel = () => {
    router.push("/admin/products")
  }

  return (
    <AdminLayout 
      title="إنشاء منتج جديد" 
      subtitle="أنشئ منتج جديد باستخدام القوالب أو من الصفر"
      actions={
        <Button 
          variant="ghost"
          onClick={() => router.push("/admin/products")}
          className="text-slate-400 hover:text-white"
        >
          <ArrowLeft className="h-4 w-4 ml-2" />
          العودة للمنتجات
        </Button>
      }
    >
      <ProductBuilder
        onSave={handleSave}
        onCancel={handleCancel}
      />
    </AdminLayout>
  )
}
