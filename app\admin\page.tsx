"use client"

import { useRouter } from "next/navigation"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { AdminLayout } from "@/components/admin/AdminLayout"
import { mockAnalytics } from "@/lib/data/mockCMSData"
import {
  Package,
  FolderOpen,
  BarChart3,
  DollarSign,
  Plus,
  TrendingUp,
  Eye,
  MousePointer,
  Activity,
  ArrowUpRight
} from "lucide-react"

export default function AdminPage() {
  const router = useRouter()

  // ## Mock stats data - will be replaced with Supabase queries
  const stats = {
    totalProducts: mockAnalytics.totalProducts,
    activeProducts: mockAnalytics.activeProducts,
    totalTemplates: mockAnalytics.totalTemplates,
    totalViews: mockAnalytics.totalViews,
    totalConversions: mockAnalytics.totalConversions,
    conversionRate: mockAnalytics.conversionRate,
    totalRevenue: 45600,
    pendingOrders: 8
  }

  const quickActions = [
    {
      title: "إنشاء منتج جديد",
      description: "أضف منتج جديد باستخدام القوالب المتاحة",
      icon: <Package className="h-6 w-6" />,
      action: () => router.push("/admin/products/create"),
      color: "from-blue-500 to-blue-600"
    },
    {
      title: "إنشاء قالب جديد",
      description: "أنشئ قالب منتج جديد قابل للإعادة الاستخدام",
      icon: <FolderOpen className="h-6 w-6" />,
      action: () => router.push("/admin/templates/create"),
      color: "from-green-500 to-green-600"
    },
    {
      title: "عرض التحليلات",
      description: "راجع أداء المنتجات والمبيعات",
      icon: <BarChart3 className="h-6 w-6" />,
      action: () => router.push("/admin/analytics"),
      color: "from-purple-500 to-purple-600"
    }
  ]

  return (
    <AdminLayout
      title="لوحة التحكم"
      subtitle="نظرة عامة على أداء المتجر والمنتجات"
      actions={
        <Button
          onClick={() => router.push("/admin/products/create")}
          className="bg-gradient-to-r from-yellow-400 to-orange-500 hover:from-yellow-500 hover:to-orange-600 text-slate-900 font-bold"
        >
          <Plus className="h-4 w-4 ml-2" />
          منتج جديد
        </Button>
      }
    >
      <div className="space-y-6">
        {/* Stats Overview */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <Card className="bg-slate-800/50 border-slate-700/50 backdrop-blur-sm">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-slate-400 text-sm font-medium">إجمالي المنتجات</p>
                  <p className="text-2xl font-bold text-white">{stats.totalProducts}</p>
                  <p className="text-green-400 text-sm flex items-center gap-1 mt-1">
                    <TrendingUp className="h-3 w-3" />
                    {stats.activeProducts} نشط
                  </p>
                </div>
                <div className="p-3 bg-blue-500/20 rounded-xl">
                  <Package className="h-6 w-6 text-blue-400" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-slate-800/50 border-slate-700/50 backdrop-blur-sm">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-slate-400 text-sm font-medium">القوالب</p>
                  <p className="text-2xl font-bold text-white">{stats.totalTemplates}</p>
                  <p className="text-slate-400 text-sm mt-1">قوالب متاحة</p>
                </div>
                <div className="p-3 bg-green-500/20 rounded-xl">
                  <FolderOpen className="h-6 w-6 text-green-400" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-slate-800/50 border-slate-700/50 backdrop-blur-sm">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-slate-400 text-sm font-medium">المشاهدات</p>
                  <p className="text-2xl font-bold text-white">{stats.totalViews.toLocaleString()}</p>
                  <p className="text-blue-400 text-sm flex items-center gap-1 mt-1">
                    <Eye className="h-3 w-3" />
                    هذا الشهر
                  </p>
                </div>
                <div className="p-3 bg-purple-500/20 rounded-xl">
                  <Eye className="h-6 w-6 text-purple-400" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-slate-800/50 border-slate-700/50 backdrop-blur-sm">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-slate-400 text-sm font-medium">معدل التحويل</p>
                  <p className="text-2xl font-bold text-white">{stats.conversionRate}%</p>
                  <p className="text-green-400 text-sm flex items-center gap-1 mt-1">
                    <MousePointer className="h-3 w-3" />
                    {stats.totalConversions} تحويل
                  </p>
                </div>
                <div className="p-3 bg-yellow-500/20 rounded-xl">
                  <Activity className="h-6 w-6 text-yellow-400" />
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Quick Actions */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {quickActions.map((action, index) => (
            <Card
              key={index}
              className="bg-slate-800/50 border-slate-700/50 backdrop-blur-sm hover:bg-slate-700/50 transition-all duration-300 cursor-pointer group"
              onClick={action.action}
            >
              <CardContent className="p-6">
                <div className="flex items-center justify-between mb-4">
                  <div className={`p-3 bg-gradient-to-r ${action.color} bg-opacity-20 rounded-xl group-hover:scale-110 transition-transform`}>
                    {action.icon}
                  </div>
                  <ArrowUpRight className="h-5 w-5 text-slate-400 group-hover:text-white transition-colors" />
                </div>
                <h3 className="text-lg font-bold text-white mb-2">{action.title}</h3>
                <p className="text-slate-400 text-sm">{action.description}</p>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Top Products */}
        <Card className="bg-slate-800/50 border-slate-700/50 backdrop-blur-sm">
          <CardHeader>
            <CardTitle className="text-white flex items-center justify-between">
              أفضل المنتجات أداءً
              <Button
                variant="ghost"
                size="sm"
                onClick={() => router.push("/admin/analytics")}
                className="text-slate-400 hover:text-white"
              >
                عرض الكل
                <ArrowUpRight className="h-4 w-4 mr-1" />
              </Button>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {mockAnalytics.topProducts.map((product, index) => (
                <div key={product.id} className="flex items-center justify-between p-4 bg-slate-700/30 rounded-lg">
                  <div className="flex items-center gap-4">
                    <div className="w-8 h-8 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-full flex items-center justify-center text-slate-900 font-bold text-sm">
                      {index + 1}
                    </div>
                    <div>
                      <p className="text-white font-medium">{product.name}</p>
                      <p className="text-slate-400 text-sm">{product.views.toLocaleString()} مشاهدة</p>
                    </div>
                  </div>
                  <div className="text-left">
                    <p className="text-green-400 font-bold">{product.conversions} تحويل</p>
                    <p className="text-slate-400 text-sm">
                      {((product.conversions / product.views) * 100).toFixed(1)}% معدل
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Recent Activity */}
        <Card className="bg-slate-800/50 border-slate-700/50 backdrop-blur-sm">
          <CardHeader>
            <CardTitle className="text-white">النشاط الأخير</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {mockAnalytics.recentActivity.map((activity, index) => (
                <div key={index} className="flex items-center gap-4 p-3 bg-slate-700/30 rounded-lg">
                  <div className={`w-2 h-2 rounded-full ${
                    activity.type === "product_created" ? "bg-green-400" :
                    activity.type === "template_updated" ? "bg-blue-400" :
                    "bg-yellow-400"
                  }`} />
                  <div className="flex-1">
                    <p className="text-white text-sm font-medium">{activity.message}</p>
                    <p className="text-slate-400 text-xs">
                      {new Date(activity.timestamp).toLocaleString('ar-EG')}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </AdminLayout>
  )
}
                  <Package className="h-16 w-16 text-slate-400 mx-auto mb-4" />
                  <h3 className="text-xl font-bold text-white mb-2">قريباً</h3>
                  <p className="text-slate-300">
                    نظام إدارة المنتجات الجديد قيد التطوير. سيتم إطلاقه قريباً.
                  </p>
                </CardContent>
              </Card>
            </div>
          </div>
        )
      case "settings":
        return (
          <div className="min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900 text-white p-6">
            <div className="max-w-4xl mx-auto">
              <h1 className="text-3xl font-bold bg-gradient-to-r from-yellow-400 to-orange-500 bg-clip-text text-transparent mb-8">
                الإعدادات
              </h1>
              <Card className="bg-slate-800/50 border-slate-700/50 backdrop-blur-sm">
                <CardContent className="p-8 text-center">
                  <Settings className="h-16 w-16 text-slate-400 mx-auto mb-4" />
                  <h3 className="text-xl font-bold text-white mb-2">قريباً</h3>
                  <p className="text-slate-300">
                    صفحة الإعدادات قيد التطوير. ستتضمن إعدادات النظام والدفع والإشعارات.
                  </p>
                </CardContent>
              </Card>
            </div>
          </div>
        )
      default: // overview
        return (
          <div className="min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900 text-white p-3 sm:p-6">
            <div className="max-w-7xl mx-auto">
              {/* Header */}
              <div className="mb-6 sm:mb-8">
                <h1 className="text-2xl sm:text-3xl font-bold bg-gradient-to-r from-yellow-400 to-orange-500 bg-clip-text text-transparent">
                  لوحة التحكم الإدارية
                </h1>
                <p className="text-slate-300 mt-2 text-sm sm:text-base">
                  مرحباً بك في لوحة التحكم - إدارة شاملة للمنتجات والطلبات
                </p>
              </div>

              {/* Stats Grid */}
              <div className="grid grid-cols-2 lg:grid-cols-4 gap-3 sm:gap-6 mb-6 sm:mb-8">
                <Card className="bg-slate-800/50 border-slate-700/50 backdrop-blur-sm">
                  <CardContent className="p-3 sm:p-6">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-slate-400 text-xs sm:text-sm">إجمالي المنتجات</p>
                        <p className="text-xl sm:text-2xl font-bold text-white">{stats.totalProducts}</p>
                        <p className="text-green-400 text-xs sm:text-sm">
                          {stats.activeProducts} نشط
                        </p>
                      </div>
                      <Package className="h-6 w-6 sm:h-8 sm:w-8 text-blue-400" />
                    </div>
                  </CardContent>
                </Card>

                <Card className="bg-slate-800/50 border-slate-700/50 backdrop-blur-sm">
                  <CardContent className="p-3 sm:p-6">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-slate-400 text-xs sm:text-sm">الفئات</p>
                        <p className="text-xl sm:text-2xl font-bold text-white">{stats.totalCategories}</p>
                        <p className="text-blue-400 text-xs sm:text-sm">
                          جميعها نشطة
                        </p>
                      </div>
                      <FolderOpen className="h-6 w-6 sm:h-8 sm:w-8 text-green-400" />
                    </div>
                  </CardContent>
                </Card>

                <Card className="bg-slate-800/50 border-slate-700/50 backdrop-blur-sm">
                  <CardContent className="p-3 sm:p-6">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-slate-400 text-xs sm:text-sm">إجمالي الطلبات</p>
                        <p className="text-xl sm:text-2xl font-bold text-white">{stats.totalOrders}</p>
                        <p className="text-yellow-400 text-xs sm:text-sm">
                          {stats.pendingOrders} في الانتظار
                        </p>
                      </div>
                      <ShoppingCart className="h-6 w-6 sm:h-8 sm:w-8 text-yellow-400" />
                    </div>
                  </CardContent>
                </Card>

                <Card className="bg-slate-800/50 border-slate-700/50 backdrop-blur-sm">
                  <CardContent className="p-3 sm:p-6">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-slate-400 text-xs sm:text-sm">إجمالي الإيرادات</p>
                        <p className="text-xl sm:text-2xl font-bold text-white">{stats.totalRevenue.toLocaleString()}</p>
                        <p className="text-green-400 text-xs sm:text-sm">
                          ج.س
                        </p>
                      </div>
                      <DollarSign className="h-6 w-6 sm:h-8 sm:w-8 text-green-400" />
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* Quick Actions */}
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6">
                <Card className="bg-slate-800/50 border-slate-700/50 backdrop-blur-sm">
                  <CardHeader>
                    <CardTitle className="text-white flex items-center gap-2">
                      <Package className="h-5 w-5" />
                      إدارة المنتجات
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <p className="text-slate-300 text-sm">
                      إضافة وتعديل وحذف المنتجات مع الحقول والخيارات الديناميكية
                    </p>
                    <Button
                      onClick={() => setActiveTab("products")}
                      className="w-full bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white"
                    >
                      إدارة المنتجات
                    </Button>
                  </CardContent>
                </Card>

                <Card className="bg-slate-800/50 border-slate-700/50 backdrop-blur-sm">
                  <CardHeader>
                    <CardTitle className="text-white flex items-center gap-2">
                      <FolderOpen className="h-5 w-5" />
                      إدارة الفئات
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <p className="text-slate-300 text-sm">
                      تنظيم المنتجات في فئات مختلفة لسهولة التصفح والإدارة
                    </p>
                    <Button
                      onClick={() => setActiveTab("categories")}
                      className="w-full bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white"
                    >
                      إدارة الفئات
                    </Button>
                  </CardContent>
                </Card>

                <Card className="bg-slate-800/50 border-slate-700/50 backdrop-blur-sm">
                  <CardHeader>
                    <CardTitle className="text-white flex items-center gap-2">
                      <BarChart3 className="h-5 w-5" />
                      التقارير والإحصائيات
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <p className="text-slate-300 text-sm">
                      عرض تقارير مفصلة عن المبيعات والطلبات والأداء العام
                    </p>
                    <Button
                      disabled
                      className="w-full bg-slate-600 text-slate-400 cursor-not-allowed"
                    >
                      قريباً
                    </Button>
                  </CardContent>
                </Card>
              </div>

              {/* System Info */}
              <div className="mt-8">
                <Card className="bg-slate-800/50 border-slate-700/50 backdrop-blur-sm">
                  <CardHeader>
                    <CardTitle className="text-white">معلومات النظام</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                      <div>
                        <p className="text-slate-400">إصدار النظام</p>
                        <p className="text-white font-medium">v1.0.0 Beta</p>
                      </div>
                      <div>
                        <p className="text-slate-400">قاعدة البيانات</p>
                        <p className="text-white font-medium">Supabase (قيد الإعداد)</p>
                      </div>
                      <div>
                        <p className="text-slate-400">آخر تحديث</p>
                        <p className="text-white font-medium">{(() => {
                          const date = new Date()
                          const year = date.getFullYear()
                          const month = String(date.getMonth() + 1).padStart(2, '0')
                          const day = String(date.getDate()).padStart(2, '0')
                          return `${day}/${month}/${year}`
                        })()}</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </div>
          </div>
        )
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900">
      {/* Navigation */}
      <div className="bg-slate-800/50 border-b border-slate-700/50 backdrop-blur-sm sticky top-0 z-50">
        <div className="max-w-7xl mx-auto px-3 sm:px-6 py-3 sm:py-4">
          <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-3 sm:gap-0">
            <div className="flex items-center gap-3 sm:gap-6 w-full sm:w-auto">
              <h1 className="text-lg sm:text-xl font-bold text-white">لوحة التحكم</h1>
              <nav className="flex flex-wrap gap-1 sm:gap-1 w-full sm:w-auto overflow-x-auto">
                {[
                  { id: "overview", label: "نظرة عامة", icon: LayoutDashboard },
                  { id: "categories", label: "الفئات", icon: FolderOpen },
                  { id: "products", label: "المنتجات", icon: Package },
                  { id: "settings", label: "الإعدادات", icon: Settings }
                ].map((tab) => (
                  <Button
                    key={tab.id}
                    variant={activeTab === tab.id ? "default" : "ghost"}
                    size="sm"
                    onClick={() => setActiveTab(tab.id as any)}
                    className={`${activeTab === tab.id 
                      ? "bg-gradient-to-r from-yellow-400 to-orange-500 text-slate-900 font-bold"
                      : "text-slate-300 hover:text-white hover:bg-slate-700/50"
                    } flex-shrink-0 text-xs sm:text-sm px-2 sm:px-3 py-1 sm:py-2`}
                  >
                    <tab.icon className="h-3 w-3 sm:h-4 sm:w-4 mr-1 sm:mr-2" />
                    <span className="hidden xs:inline sm:inline">{tab.label}</span>
                    <span className="xs:hidden sm:hidden">
                      {tab.id === "overview" ? "عام" : tab.id === "categories" ? "فئات" : tab.id === "products" ? "منتجات" : "إعدادات"}
                    </span>
                  </Button>
                ))}
              </nav>
            </div>
            
            <Badge className="bg-green-500/20 text-green-400 border-green-500/30 text-xs sm:text-sm">
              متصل
            </Badge>
          </div>
        </div>
      </div>

      {/* Content */}
      {renderContent()}
    </div>
  )
}
