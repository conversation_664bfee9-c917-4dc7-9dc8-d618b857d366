"use client"

import React, { useState } from "react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, DialogTitle } from "@/components/ui/dialog"
import { Card, CardContent } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import {
  Type,
  Hash,
  Mail,
  FileText,
  List,
  Package,
  Image,
  CheckSquare,
  DollarSign,
  Minus,
  Heading,
  Search,
  Zap,
  Star,
  X
} from "lucide-react"
import { FieldType } from "@/lib/types"

interface FieldTypeSelectorProps {
  onSelect: (type: FieldType) => void
  onClose: () => void
}

interface FieldTypeOption {
  type: FieldType
  label: string
  description: string
  icon: React.ReactNode
  category: "basic" | "advanced" | "special"
  popular?: boolean
}

const fieldTypeOptions: FieldTypeOption[] = [
  // Basic Fields
  {
    type: "text",
    label: "حقل نص",
    description: "حقل إدخال نص بسيط (مثل اسم اللاعب، معرف المستخدم)",
    icon: <Type className="h-5 w-5" />,
    category: "basic",
    popular: true
  },
  {
    type: "email",
    label: "بريد إلكتروني",
    description: "حقل إدخال البريد الإلكتروني مع التحقق التلقائي",
    icon: <Mail className="h-5 w-5" />,
    category: "basic"
  },
  {
    type: "number",
    label: "رقم",
    description: "حقل إدخال الأرقام (مثل معرف اللاعب، رقم الهاتف)",
    icon: <Hash className="h-5 w-5" />,
    category: "basic",
    popular: true
  },
  {
    type: "textarea",
    label: "نص طويل",
    description: "حقل إدخال نص متعدد الأسطر للملاحظات والتعليقات",
    icon: <FileText className="h-5 w-5" />,
    category: "basic"
  },

  // Advanced Fields
  {
    type: "select",
    label: "قائمة منسدلة",
    description: "قائمة خيارات للاختيار من بينها (مثل السيرفر، المنطقة)",
    icon: <List className="h-5 w-5" />,
    category: "advanced",
    popular: true
  },
  {
    type: "radio",
    label: "اختيار واحد",
    description: "مجموعة خيارات للاختيار من بينها (خيار واحد فقط)",
    icon: <List className="h-5 w-5" />,
    category: "advanced"
  },
  {
    type: "checkbox",
    label: "مربع اختيار",
    description: "مربع اختيار للموافقة أو تفعيل خيار معين",
    icon: <CheckSquare className="h-5 w-5" />,
    category: "advanced"
  },
  {
    type: "quantity_selector",
    label: "محدد الكمية",
    description: "حقل لاختيار الكمية مع أزرار زيادة ونقصان",
    icon: <Hash className="h-5 w-5" />,
    category: "advanced"
  },

  // Special Fields
  {
    type: "package_selector",
    label: "محدد الحزم",
    description: "عرض حزم المنتجات مع الأسعار (مثل حزم الجواهر، اليوسي)",
    icon: <Package className="h-5 w-5" />,
    category: "special",
    popular: true
  },
  {
    type: "image",
    label: "رفع صورة",
    description: "حقل لرفع الصور والملفات",
    icon: <Image className="h-5 w-5" />,
    category: "special"
  },
  {
    type: "price_display",
    label: "عرض السعر",
    description: "عرض السعر النهائي مع الخصومات والعروض",
    icon: <DollarSign className="h-5 w-5" />,
    category: "special"
  },
  {
    type: "heading",
    label: "عنوان",
    description: "عنوان لتقسيم النموذج إلى أقسام",
    icon: <Heading className="h-5 w-5" />,
    category: "special"
  },
  {
    type: "divider",
    label: "فاصل",
    description: "خط فاصل لتنظيم النموذج بصرياً",
    icon: <Minus className="h-5 w-5" />,
    category: "special"
  }
]

const categoryLabels = {
  basic: "الحقول الأساسية",
  advanced: "الحقول المتقدمة", 
  special: "الحقول الخاصة"
}

const categoryIcons = {
  basic: <Type className="h-4 w-4" />,
  advanced: <Zap className="h-4 w-4" />,
  special: <Star className="h-4 w-4" />
}

export function FieldTypeSelector({ onSelect, onClose }: FieldTypeSelectorProps) {
  const [searchTerm, setSearchTerm] = useState("")
  const [selectedCategory, setSelectedCategory] = useState<"all" | "basic" | "advanced" | "special">("all")

  // Filter field types based on search and category
  const filteredFields = fieldTypeOptions.filter(field => {
    const matchesSearch = field.label.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         field.description.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesCategory = selectedCategory === "all" || field.category === selectedCategory
    
    return matchesSearch && matchesCategory
  })

  // Group fields by category
  const groupedFields = filteredFields.reduce((acc, field) => {
    if (!acc[field.category]) {
      acc[field.category] = []
    }
    acc[field.category].push(field)
    return acc
  }, {} as Record<string, FieldTypeOption[]>)

  return (
    <div className="fixed inset-0 z-50 bg-black/80 flex items-center justify-center p-2 sm:p-4">
      <div className="bg-slate-800 border border-slate-700 rounded-lg w-full max-w-4xl max-h-[95vh] sm:max-h-[90vh] overflow-hidden text-white relative">
        {/* Close Button */}
        <button
          onClick={onClose}
          className="absolute top-3 left-3 z-10 p-2 rounded-lg bg-slate-700/50 hover:bg-slate-600 transition-colors"
        >
          <X className="h-4 w-4 text-slate-300" />
        </button>

        {/* Header */}
        <div className="p-4 sm:p-6 pb-0">
          <h2 className="text-lg sm:text-xl font-bold bg-gradient-to-r from-yellow-400 to-orange-500 bg-clip-text text-transparent">
            اختر نوع الحقل
          </h2>
        </div>

        <div className="p-4 sm:p-6 pt-0 max-h-[85vh] sm:max-h-[80vh] overflow-y-auto space-y-4">
          {/* Search and Filter */}
          <div className="flex flex-col gap-3">
            <div className="relative">
              <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-slate-400 h-4 w-4" />
              <Input
                placeholder="ابحث عن نوع الحقل..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pr-10 bg-slate-700/50 border-slate-600 text-white text-sm"
              />
            </div>

            <div className="flex gap-2 overflow-x-auto pb-2">
              <button
                onClick={() => setSelectedCategory("all")}
                className={`px-3 py-2 rounded-lg text-xs sm:text-sm font-medium whitespace-nowrap transition-colors ${
                  selectedCategory === "all"
                    ? "bg-blue-500 text-white"
                    : "bg-slate-700 text-slate-300 hover:bg-slate-600"
                }`}
              >
                الكل
              </button>
              {Object.entries(categoryLabels).map(([key, label]) => (
                <button
                  key={key}
                  onClick={() => setSelectedCategory(key as any)}
                  className={`px-3 py-2 rounded-lg text-xs sm:text-sm font-medium whitespace-nowrap transition-colors flex items-center gap-1 ${
                    selectedCategory === key
                      ? "bg-blue-500 text-white"
                      : "bg-slate-700 text-slate-300 hover:bg-slate-600"
                  }`}
                >
                  {categoryIcons[key as keyof typeof categoryIcons]}
                  <span className="hidden sm:inline">{label}</span>
                </button>
              ))}
            </div>
          </div>

          {/* Field Types Grid */}
          <div className="space-y-4 sm:space-y-6">
            {Object.entries(groupedFields).map(([category, fields]) => (
              <div key={category}>
                <h3 className="text-base sm:text-lg font-semibold text-white mb-3 flex items-center gap-2">
                  {categoryIcons[category as keyof typeof categoryIcons]}
                  {categoryLabels[category as keyof typeof categoryLabels]}
                </h3>

                <div className="grid grid-cols-1 gap-3">
                  {fields.map((field) => (
                    <Card
                      key={field.type}
                      className="bg-slate-700/50 border-slate-600 hover:bg-slate-600/50 transition-all duration-200 cursor-pointer active:scale-95"
                      onClick={() => onSelect(field.type)}
                    >
                      <CardContent className="p-3 sm:p-4">
                        <div className="flex items-start gap-3">
                          <div className="p-2 bg-slate-600/50 rounded-lg text-blue-400 flex-shrink-0">
                            {field.icon}
                          </div>

                          <div className="flex-1 min-w-0">
                            <div className="flex items-center gap-2 mb-1">
                              <h4 className="font-medium text-white text-sm sm:text-base">{field.label}</h4>
                              {field.popular && (
                                <Badge className="bg-yellow-500/20 text-yellow-400 text-xs">
                                  شائع
                                </Badge>
                              )}
                            </div>
                            <p className="text-xs sm:text-sm text-slate-300 leading-relaxed">
                              {field.description}
                            </p>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </div>
            ))}
          </div>

          {filteredFields.length === 0 && (
            <div className="text-center py-8">
              <Search className="h-12 w-12 text-slate-400 mx-auto mb-3" />
              <h3 className="text-base sm:text-lg font-medium text-white mb-1">لا توجد نتائج</h3>
              <p className="text-sm text-slate-400">جرب البحث بكلمات مختلفة أو اختر فئة أخرى</p>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
