// Common types used across the application

export interface GameCard {
  title: string
  subtitle: string
  gradient: string
  icon: string
}

export interface MenuItem {
  icon: React.ReactNode
  label: string
  href: string
}

export interface Slide {
  id: number
  title: string
  subtitle: string
  buttonText: string
  gradient: string
  image: string
}

export interface Feature {
  icon: string
  title: string
  desc: string
}

export interface Stat {
  number: string
  label: string
}

export interface NavigationItem {
  id: string
  icon: React.ReactNode
  label: string
  center?: boolean
}

// Wallet and Currency Types
export type Currency = "SDG" | "EGP"

export interface CurrencyInfo {
  code: Currency
  name: string
  symbol: string
  arabicName: string
}

export interface WalletBalance {
  currency: Currency
  amount: number
  lastUpdated: Date
}

export interface Transaction {
  id: string
  type: "deposit" | "withdrawal" | "purchase"
  amount: number
  currency: Currency
  description: string
  date: Date
  status: "completed" | "pending" | "failed"
  reference?: string
}

export interface WalletData {
  balances: WalletBalance[]
  selectedCurrency: Currency
  totalPurchases: number
  transactions: Transaction[]
}

// Dynamic CMS Product System Types
export type FieldType =
  | "text"
  | "email"
  | "number"
  | "textarea"
  | "select"
  | "radio"
  | "checkbox"
  | "image"
  | "package_selection"
  | "server_selection"
  | "region_selection"
  | "custom_info"

export interface FieldOption {
  id: string
  label: string
  value: string
  price?: number
  originalPrice?: number
  discount?: number
  popular?: boolean
}

export interface FieldValidation {
  required?: boolean
  minLength?: number
  maxLength?: number
  pattern?: string
  min?: number
  max?: number
  customMessage?: string
}

export interface DynamicField {
  id: string
  type: FieldType
  name: string
  label: string
  placeholder?: string
  description?: string
  validation: FieldValidation
  options?: FieldOption[]
  defaultValue?: string | number | boolean
  order: number
  isVisible: boolean
  isRequired: boolean
  dependsOn?: string // Field ID that this field depends on
  dependsOnValue?: string // Value that triggers this field to show
  customProps?: Record<string, any>
}

export interface ProductTemplate {
  id: string
  name: string
  description: string
  fields: DynamicField[]
  layout: "single_column" | "two_column" | "custom"
  mobileLayout?: "single_column" | "two_column"
  createdAt: Date
  updatedAt: Date
  isActive: boolean
  category?: string
}

export interface DynamicProduct {
  id: string
  templateId: string
  slug: string
  title: string
  shortDescription: string
  fullDescription?: string
  image: string
  category: string
  rating?: number
  reviewsCount?: number
  basePrice?: number
  priceRange?: string
  estimatedTime?: string
  inStock: boolean
  isActive: boolean
  fields: DynamicField[]
  metadata: {
    seoTitle?: string
    seoDescription?: string
    tags?: string[]
    featured?: boolean
    popular?: boolean
  }
  createdAt: Date
  updatedAt: Date
}

export interface ProductFormData {
  [fieldName: string]: string | number | boolean | string[]
}

export interface FieldRenderProps {
  field: DynamicField
  value: any
  onChange: (value: any) => void
  error?: string
  disabled?: boolean
}

// Checkout System Types
export interface BankAccount {
  id: string
  name: string
  accountNumber: string
  logoUrl?: string
  isActive: boolean
}

export interface RechargeOption {
  id: string
  amount: number
  currency: Currency
  isActive: boolean
}

export interface CheckoutUserDetails {
  firstName: string
  lastName: string
  phone: string
  email: string
}

export interface CheckoutData {
  step: 1 | 2 | 3
  amount: number
  currency: Currency
  userDetails: CheckoutUserDetails | null
  selectedBank: BankAccount | null
  referenceNumber: string
  receiptFile: File | null
  receiptPreview: string | null
}

export interface CheckoutOrder {
  id: string
  amount: number
  currency: Currency
  userDetails: CheckoutUserDetails
  selectedBank: BankAccount
  referenceNumber: string
  receiptFileName?: string
  status: "pending" | "completed" | "failed"
  createdAt: Date
}

export interface CheckoutConfig {
  bankAccounts: BankAccount[]
  rechargeOptions: RechargeOption[]
  notes: string[]
  lastUpdated: Date
}
