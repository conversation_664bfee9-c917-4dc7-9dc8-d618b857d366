// Common types used across the application

export interface GameCard {
  title: string
  subtitle: string
  gradient: string
  icon: string
}

export interface MenuItem {
  icon: React.ReactNode
  label: string
  href: string
}

export interface Slide {
  id: number
  title: string
  subtitle: string
  buttonText: string
  gradient: string
  image: string
}

export interface Feature {
  icon: string
  title: string
  desc: string
}

export interface Stat {
  number: string
  label: string
}

export interface NavigationItem {
  id: string
  icon: React.ReactNode
  label: string
  center?: boolean
}

// ===== DYNAMIC CMS SYSTEM TYPES =====

// Core field types supported by the CMS
export type FieldType =
  | "text"
  | "email"
  | "number"
  | "textarea"
  | "select"
  | "package_selector"
  | "image"
  | "checkbox"
  | "radio"
  | "quantity_selector"
  | "price_display"
  | "divider"
  | "heading"

// Base interface for all field types
export interface BaseField {
  id: string
  type: FieldType
  label: string
  name: string
  required: boolean
  order: number
  visible: boolean
  description?: string
  placeholder?: string
  validation?: FieldValidation
}

// Validation rules for fields
export interface FieldValidation {
  min?: number
  max?: number
  pattern?: string
  customMessage?: string
}

// Text input field
export interface TextField extends BaseField {
  type: "text" | "email" | "textarea"
  maxLength?: number
  minLength?: number
}

// Number input field
export interface NumberField extends BaseField {
  type: "number" | "quantity_selector"
  min?: number
  max?: number
  step?: number
  defaultValue?: number
}

// Select dropdown field
export interface SelectField extends BaseField {
  type: "select" | "radio"
  options: SelectOption[]
  multiple?: boolean
}

export interface SelectOption {
  id: string
  label: string
  value: string
  price?: number
  description?: string
  image?: string
}

// Package selector (for gaming products)
export interface PackageSelectorField extends BaseField {
  type: "package_selector"
  packages: Package[]
  allowMultiple?: boolean
  displayStyle: "grid" | "list" | "cards"
}

export interface Package {
  id: string
  name: string
  description?: string
  amount: string
  price: number
  originalPrice?: number
  discount?: number
  popular?: boolean
  image?: string
  features?: string[]
}

// Image field
export interface ImageField extends BaseField {
  type: "image"
  maxSize?: number // in MB
  allowedTypes?: string[]
  multiple?: boolean
}

// Checkbox field
export interface CheckboxField extends BaseField {
  type: "checkbox"
  defaultChecked?: boolean
}

// Price display field (read-only)
export interface PriceDisplayField extends BaseField {
  type: "price_display"
  currency: string
  showOriginalPrice?: boolean
  showDiscount?: boolean
}

// Divider field (visual separator)
export interface DividerField extends BaseField {
  type: "divider"
  style: "line" | "space" | "gradient"
  height?: number
}

// Heading field (section titles)
export interface HeadingField extends BaseField {
  type: "heading"
  level: 1 | 2 | 3 | 4 | 5 | 6
  alignment: "left" | "center" | "right"
  color?: string
}

// Union type for all field types
export type DynamicField =
  | TextField
  | NumberField
  | SelectField
  | PackageSelectorField
  | ImageField
  | CheckboxField
  | PriceDisplayField
  | DividerField
  | HeadingField

// Product template system
export interface ProductTemplate {
  id: string
  name: string
  description?: string
  category: string
  fields: DynamicField[]
  layout: ProductLayout
  createdAt: Date
  updatedAt: Date
  isDefault?: boolean
  previewImage?: string
}

// Layout configuration for products
export interface ProductLayout {
  sections: LayoutSection[]
  mobileLayout?: LayoutSection[]
  theme: ProductTheme
}

export interface LayoutSection {
  id: string
  name: string
  fieldIds: string[]
  columns: number
  spacing: "tight" | "normal" | "loose"
  background?: string
  padding?: string
  order: number
}

export interface ProductTheme {
  primaryColor: string
  secondaryColor: string
  backgroundColor: string
  textColor: string
  borderRadius: "none" | "small" | "medium" | "large"
  shadows: boolean
  animations: boolean
}

// Dynamic product instance (created from template)
export interface DynamicProduct {
  id: string
  templateId: string
  slug: string
  title: string
  description?: string
  category: string
  status: "draft" | "published" | "archived"
  fields: DynamicField[]
  layout: ProductLayout
  seo: ProductSEO
  pricing: ProductPricing
  createdAt: Date
  updatedAt: Date
  publishedAt?: Date
}

export interface ProductSEO {
  metaTitle?: string
  metaDescription?: string
  keywords?: string[]
  ogImage?: string
}

export interface ProductPricing {
  basePrice?: number
  currency: string
  priceOverride?: boolean // If true, package/option prices override base price
  taxIncluded: boolean
  showPriceRange: boolean
}

// Form submission data
export interface ProductFormData {
  [fieldName: string]: any
}

// CMS Admin interfaces
export interface CMSSettings {
  allowedFileTypes: string[]
  maxFileSize: number
  defaultCurrency: string
  enablePreview: boolean
  autoSave: boolean
  theme: AdminTheme
}

export interface AdminTheme {
  darkMode: boolean
  primaryColor: string
  sidebarCollapsed: boolean
}

// Field creation wizard
export interface FieldWizardStep {
  id: string
  title: string
  description: string
  component: React.ComponentType<any>
  validation?: (data: any) => boolean
}

// Drag and drop interfaces
export interface DragDropField {
  id: string
  type: FieldType
  label: string
  isDragging?: boolean
  isOver?: boolean
}

export interface DropZone {
  id: string
  accepts: FieldType[]
  onDrop: (field: DragDropField) => void
  children: React.ReactNode
}

// Wallet and Currency Types
export type Currency = "SDG" | "EGP"

export interface CurrencyInfo {
  code: Currency
  name: string
  symbol: string
  arabicName: string
}

export interface WalletBalance {
  currency: Currency
  amount: number
  lastUpdated: Date
}

export interface Transaction {
  id: string
  type: "deposit" | "withdrawal" | "purchase"
  amount: number
  currency: Currency
  description: string
  date: Date
  status: "completed" | "pending" | "failed"
  reference?: string
}

export interface WalletData {
  balances: WalletBalance[]
  selectedCurrency: Currency
  totalPurchases: number
  transactions: Transaction[]
}

// Checkout System Types
export interface BankAccount {
  id: string
  name: string
  accountNumber: string
  logoUrl?: string
  isActive: boolean
}

export interface RechargeOption {
  id: string
  amount: number
  currency: Currency
  isActive: boolean
}

export interface CheckoutUserDetails {
  firstName: string
  lastName: string
  phone: string
  email: string
}

export interface CheckoutData {
  step: 1 | 2 | 3
  amount: number
  currency: Currency
  userDetails: CheckoutUserDetails | null
  selectedBank: BankAccount | null
  referenceNumber: string
  receiptFile: File | null
  receiptPreview: string | null
}

export interface CheckoutOrder {
  id: string
  amount: number
  currency: Currency
  userDetails: CheckoutUserDetails
  selectedBank: BankAccount
  referenceNumber: string
  receiptFileName?: string
  status: "pending" | "completed" | "failed"
  createdAt: Date
}

export interface CheckoutConfig {
  bankAccounts: BankAccount[]
  rechargeOptions: RechargeOption[]
  notes: string[]
  lastUpdated: Date
}
