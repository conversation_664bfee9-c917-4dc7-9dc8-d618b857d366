"use client"

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { ShoppingCart, Zap, Clock } from "lucide-react"

interface ProductInstantProps {
  product: {
    id: string
    name: string
    description?: string
    category: string
    price?: number
    deliveryTime?: string
    requirements?: string[]
  }
}

export function ProductInstant({ product }: ProductInstantProps) {
  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900">
      <div className="container mx-auto px-4 py-8">
        <Card className="max-w-4xl mx-auto bg-slate-800/50 border-slate-700/50 backdrop-blur-sm">
          <CardHeader>
            <div className="text-center space-y-4">
              <div className="flex items-center justify-center gap-2">
                <Zap className="h-6 w-6 text-yellow-400" />
                <h1 className="text-2xl lg:text-3xl font-bold bg-gradient-to-r from-yellow-400 to-orange-500 bg-clip-text text-transparent">
                  {product.name}
                </h1>
              </div>
              {product.description && (
                <p className="text-slate-300">{product.description}</p>
              )}
              <div className="flex items-center justify-center gap-4">
                <Badge variant="secondary">{product.category}</Badge>
                {product.deliveryTime && (
                  <Badge className="bg-green-500/20 text-green-400">
                    <Clock className="h-3 w-3 ml-1" />
                    {product.deliveryTime}
                  </Badge>
                )}
              </div>
            </div>
          </CardHeader>
          
          <CardContent className="space-y-6">
            <div className="bg-yellow-500/10 border border-yellow-500/20 rounded-lg p-4">
              <div className="flex items-center gap-2 mb-2">
                <Zap className="h-5 w-5 text-yellow-400" />
                <h3 className="font-semibold text-yellow-400">توصيل فوري</h3>
              </div>
              <p className="text-sm text-slate-300">
                سيتم توصيل طلبك خلال دقائق معدودة بعد إتمام الدفع
              </p>
            </div>

            {product.requirements && (
              <div className="space-y-3">
                <h3 className="text-lg font-semibold text-white">المطلوب للتوصيل</h3>
                <div className="space-y-3">
                  {product.requirements.map((requirement, index) => (
                    <div key={index} className="space-y-2">
                      <Label className="text-slate-300">{requirement}</Label>
                      <Input
                        placeholder={`أدخل ${requirement}...`}
                        className="bg-slate-700/50 border-slate-600 text-white"
                      />
                    </div>
                  ))}
                </div>
              </div>
            )}

            {product.price && (
              <div className="text-center space-y-2">
                <div className="text-3xl font-bold text-green-400">
                  {product.price} ج.س.
                </div>
              </div>
            )}

            <div className="flex flex-col sm:flex-row gap-3 pt-6 border-t border-slate-700">
              <Button className="flex-1 bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700">
                <ShoppingCart className="h-4 w-4 ml-2" />
                شراء فوري
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
