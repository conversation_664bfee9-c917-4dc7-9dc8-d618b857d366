"use client"

import { useState } from "react"
import { AppHeader } from "@/components/layout/AppHeader"
import { SideMenu } from "@/components/layout/SideMenu"
import { NewsTicket } from "@/components/shared/NewsTicket"
import { MobileNavigation } from "@/components/layout/MobileNavigation"
import { DesktopFooter } from "@/components/layout/DesktopFooter"
import { WalletBalance } from "@/components/wallet/WalletBalance"
import { WalletTransactions } from "@/components/wallet/WalletTransactions"
import { mockWalletData } from "@/lib/data/mockWalletData"
import { Currency } from "@/lib/types"
import { Wallet } from "lucide-react"
import { useRouter } from "next/navigation"

export function WalletPage() {
  const [activeTab, setActiveTab] = useState("wallet") // Set to wallet tab since this is wallet page
  const [isMenuOpen, setIsMenuOpen] = useState(false)
  const [selectedCurrency, setSelectedCurrency] = useState<Currency>("SDG")
  const [isLoading, setIsLoading] = useState(false)
  const [walletData, setWalletData] = useState(mockWalletData)
  const router = useRouter()

  // ## Handler for currency change - will trigger Supabase update
  const handleCurrencyChange = (currency: Currency) => {
    setSelectedCurrency(currency)
    // ## TODO: Update user preference in Supabase
    // ## TODO: Fetch balance for selected currency from Supabase
  }

  // ## Handler for adding balance - navigates to checkout page
  const handleAddBalance = () => {
    router.push("/checkout")
  }

  // Navigation handler for navbar
  const handleTabChange = (tab: string) => {
    if (tab === "wallet") {
      router.push("/wallet")
    } else if (tab === "profile") {
      router.push("/profile")
    } else if (tab === "shop") {
      router.push("/shop")
    } else if (tab === "home") {
      router.push("/")
      router.refresh()
    } else {
      setActiveTab(tab)
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900 text-white relative overflow-hidden">
      {/* Background Effects */}
      <div className="absolute inset-0 bg-[radial-gradient(ellipse_at_top,_var(--tw-gradient-stops))] from-yellow-400/20 via-transparent to-transparent" />
      <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-yellow-400/10 rounded-full blur-3xl" />
      <div className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-orange-500/10 rounded-full blur-3xl" />

      <AppHeader onMenuOpen={() => setIsMenuOpen(true)} />
      <NewsTicket />
      <SideMenu isOpen={isMenuOpen} onClose={() => setIsMenuOpen(false)} />

      {/* Main Content */}
      <main className="relative z-10 p-4 lg:p-8 space-y-6 lg:space-y-8 pb-32 pt-32 lg:pt-36 max-w-7xl mx-auto">
        {/* Page Header */}
        <div className="text-center space-y-4">
          <div className="flex items-center justify-center gap-4 mb-4">
            <div className="p-3 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-2xl shadow-lg">
              <Wallet className="h-8 w-8 text-slate-900" />
            </div>
          </div>
          <h1 className="text-3xl lg:text-4xl font-bold bg-gradient-to-r from-yellow-400 to-orange-500 bg-clip-text text-transparent">
            محفظتي
          </h1>
          <p className="text-slate-300 text-base lg:text-lg max-w-md mx-auto">
            إدارة رصيدك ومعاملاتك المالية بسهولة وأمان
          </p>
        </div>

        {/* Wallet Balance Section */}
        <WalletBalance
          walletData={walletData}
          selectedCurrency={selectedCurrency}
          onCurrencyChange={handleCurrencyChange}
          onAddBalance={handleAddBalance}
          isLoading={isLoading}
        />

        {/* Transactions Section */}
        <WalletTransactions
          transactions={walletData.transactions}
          selectedCurrency={selectedCurrency}
          isLoading={isLoading}
        />
      </main>

      <MobileNavigation activeTab={activeTab} onTabChange={handleTabChange} />
      <DesktopFooter activeTab={activeTab} onTabChange={handleTabChange} />
    </div>
  )
}
