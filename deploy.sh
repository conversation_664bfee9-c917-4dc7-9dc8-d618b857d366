#!/bin/bash

# Alraya Store - Deployment Script
echo "🚀 Deploying Alraya Store to Vercel..."

# Check if git is initialized
if [ ! -d ".git" ]; then
    echo "📦 Initializing Git repository..."
    git init
    git branch -M main
fi

# Add all files
echo "📁 Adding files to Git..."
git add .

# Commit changes
echo "💾 Committing changes..."
git commit -m "Deploy: Alraya Store Dynamic CMS System

✨ Features:
- Complete Dynamic Product CMS
- 13 Field Types with validation
- Mobile-responsive admin interface
- Real-time preview with interactive mode
- Image upload system
- Arabic RTL support

🔧 Technical:
- Next.js 15 with App Router
- TypeScript + Tailwind CSS
- Vercel optimized build
- Production ready"

# Check if Vercel CLI is installed
if ! command -v vercel &> /dev/null; then
    echo "📦 Installing Vercel CLI..."
    npm install -g vercel
fi

# Deploy to Vercel
echo "🚀 Deploying to Vercel..."
vercel --prod

echo "✅ Deployment complete!"
echo ""
echo "🎉 Your Alraya Store is now live!"
echo "📱 Admin Panel: https://your-domain.vercel.app/admin"
echo "🏪 Store Front: https://your-domain.vercel.app"
echo ""
echo "🔧 Next Steps:"
echo "1. Configure your custom domain in Vercel dashboard"
echo "2. Set up environment variables if needed"
echo "3. Start creating your product templates!"
