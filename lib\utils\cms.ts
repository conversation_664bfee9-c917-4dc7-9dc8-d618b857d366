// CMS utility functions for dynamic product management

import { <PERSON><PERSON>Field, DynamicProduct, ProductTemplate, FieldType, FieldValidation, ProductFormData } from "@/lib/types"
import { ValidationResult, ValidationRule } from "@/lib/types/cms"

// Field Generation Utilities
export function generateFieldId(): string {
  return `field_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
}

export function generateProductId(): string {
  return `product_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
}

export function generateTemplateId(): string {
  return `template_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
}

export function generateSlugFromTitle(title: string): string {
  return title
    .toLowerCase()
    .replace(/[^\w\s-]/g, '') // Remove special characters
    .replace(/\s+/g, '-') // Replace spaces with hyphens
    .replace(/-+/g, '-') // Replace multiple hyphens with single
    .trim()
}

// Field Manipulation Utilities
export function createDefaultField(type: FieldType, order: number): DynamicField {
  const baseField: DynamicField = {
    id: generateFieldId(),
    type,
    name: `field_${order}`,
    label: getDefaultFieldLabel(type),
    order,
    isVisible: true,
    isRequired: false,
    validation: {}
  }

  // Add type-specific defaults
  switch (type) {
    case "text":
      return {
        ...baseField,
        placeholder: "أدخل النص هنا...",
        validation: { required: false, minLength: 1, maxLength: 100 }
      }
    case "email":
      return {
        ...baseField,
        placeholder: "<EMAIL>",
        validation: { required: false, pattern: "^[^@]+@[^@]+\\.[^@]+$" }
      }
    case "number":
      return {
        ...baseField,
        placeholder: "أدخل رقم...",
        validation: { required: false, min: 0, max: 999999 }
      }
    case "select":
    case "package_selection":
    case "server_selection":
      return {
        ...baseField,
        options: [],
        validation: { required: false }
      }
    case "textarea":
      return {
        ...baseField,
        placeholder: "أدخل النص الطويل هنا...",
        validation: { required: false, minLength: 1, maxLength: 500 }
      }
    default:
      return baseField
  }
}

function getDefaultFieldLabel(type: FieldType): string {
  const labels: Record<FieldType, string> = {
    text: "حقل نص",
    email: "البريد الإلكتروني",
    number: "رقم",
    textarea: "نص طويل",
    select: "قائمة اختيار",
    radio: "اختيار واحد",
    checkbox: "اختيار متعدد",
    image: "صورة",
    package_selection: "اختيار الباقة",
    server_selection: "اختيار السيرفر",
    region_selection: "اختيار المنطقة",
    custom_info: "معلومات مخصصة"
  }
  return labels[type] || "حقل"
}

// Field Ordering Utilities
export function reorderFields(fields: DynamicField[], fromIndex: number, toIndex: number): DynamicField[] {
  const result = Array.from(fields)
  const [removed] = result.splice(fromIndex, 1)
  result.splice(toIndex, 0, removed)
  
  // Update order values
  return result.map((field, index) => ({
    ...field,
    order: index
  }))
}

export function insertFieldAtPosition(fields: DynamicField[], newField: DynamicField, position: number): DynamicField[] {
  const result = Array.from(fields)
  result.splice(position, 0, { ...newField, order: position })
  
  // Update order values for all fields
  return result.map((field, index) => ({
    ...field,
    order: index
  }))
}

// Validation Utilities
export function validateField(field: DynamicField, value: any): string | null {
  const { validation } = field
  
  // Required validation
  if (validation.required && (!value || value === "")) {
    return validation.customMessage || `${field.label} مطلوب`
  }
  
  // Skip other validations if field is empty and not required
  if (!value || value === "") {
    return null
  }
  
  // Type-specific validations
  switch (field.type) {
    case "text":
    case "textarea":
      return validateTextField(field, value, validation)
    case "email":
      return validateEmailField(field, value, validation)
    case "number":
      return validateNumberField(field, value, validation)
    default:
      return null
  }
}

function validateTextField(field: DynamicField, value: string, validation: FieldValidation): string | null {
  if (validation.minLength && value.length < validation.minLength) {
    return `${field.label} يجب أن يكون على الأقل ${validation.minLength} أحرف`
  }
  
  if (validation.maxLength && value.length > validation.maxLength) {
    return `${field.label} يجب أن يكون أقل من ${validation.maxLength} حرف`
  }
  
  if (validation.pattern) {
    const regex = new RegExp(validation.pattern)
    if (!regex.test(value)) {
      return validation.customMessage || `${field.label} غير صحيح`
    }
  }
  
  return null
}

function validateEmailField(field: DynamicField, value: string, validation: FieldValidation): string | null {
  const emailRegex = /^[^@]+@[^@]+\.[^@]+$/
  if (!emailRegex.test(value)) {
    return validation.customMessage || "البريد الإلكتروني غير صحيح"
  }
  return null
}

function validateNumberField(field: DynamicField, value: number, validation: FieldValidation): string | null {
  if (validation.min !== undefined && value < validation.min) {
    return `${field.label} يجب أن يكون على الأقل ${validation.min}`
  }
  
  if (validation.max !== undefined && value > validation.max) {
    return `${field.label} يجب أن يكون أقل من ${validation.max}`
  }
  
  return null
}

export function validateFormData(fields: DynamicField[], formData: ProductFormData): ValidationResult {
  const errors: Record<string, string> = {}
  
  for (const field of fields) {
    if (!field.isVisible) continue
    
    const value = formData[field.name]
    const error = validateField(field, value)
    
    if (error) {
      errors[field.name] = error
    }
  }
  
  return {
    isValid: Object.keys(errors).length === 0,
    errors
  }
}

// Template Utilities
export function createEmptyTemplate(): ProductTemplate {
  return {
    id: generateTemplateId(),
    name: "قالب جديد",
    description: "قالب منتج جديد",
    fields: [],
    layout: "single_column",
    createdAt: new Date(),
    updatedAt: new Date(),
    isActive: true
  }
}

export function duplicateTemplate(template: ProductTemplate): ProductTemplate {
  return {
    ...template,
    id: generateTemplateId(),
    name: `${template.name} - نسخة`,
    fields: template.fields.map(field => ({
      ...field,
      id: generateFieldId()
    })),
    createdAt: new Date(),
    updatedAt: new Date()
  }
}

// Product Utilities
export function createProductFromTemplate(template: ProductTemplate): DynamicProduct {
  return {
    id: generateProductId(),
    templateId: template.id,
    slug: "",
    title: "منتج جديد",
    shortDescription: "وصف قصير للمنتج",
    image: "",
    category: "",
    inStock: true,
    isActive: false,
    fields: template.fields.map(field => ({
      ...field,
      id: generateFieldId()
    })),
    metadata: {
      featured: false,
      popular: false,
      tags: []
    },
    createdAt: new Date(),
    updatedAt: new Date()
  }
}

export function duplicateProduct(product: DynamicProduct): DynamicProduct {
  return {
    ...product,
    id: generateProductId(),
    slug: `${product.slug}-copy`,
    title: `${product.title} - نسخة`,
    fields: product.fields.map(field => ({
      ...field,
      id: generateFieldId()
    })),
    isActive: false,
    createdAt: new Date(),
    updatedAt: new Date()
  }
}

// Price Calculation Utilities
export function calculateProductPrice(fields: DynamicField[], formData: ProductFormData): number {
  let totalPrice = 0
  
  for (const field of fields) {
    if (field.type === "package_selection" && field.options) {
      const selectedOption = field.options.find(option => option.value === formData[field.name])
      if (selectedOption && selectedOption.price) {
        totalPrice += selectedOption.price
      }
    }
  }
  
  return totalPrice
}

// Export/Import Utilities
export function exportTemplateToJSON(template: ProductTemplate): string {
  return JSON.stringify(template, null, 2)
}

export function importTemplateFromJSON(jsonString: string): ProductTemplate {
  const template = JSON.parse(jsonString)
  
  // Regenerate IDs to avoid conflicts
  return {
    ...template,
    id: generateTemplateId(),
    fields: template.fields.map((field: DynamicField) => ({
      ...field,
      id: generateFieldId()
    })),
    createdAt: new Date(),
    updatedAt: new Date()
  }
}

// Search and Filter Utilities
export function filterProducts(products: DynamicProduct[], searchTerm: string): DynamicProduct[] {
  if (!searchTerm.trim()) return products
  
  const term = searchTerm.toLowerCase()
  return products.filter(product => 
    product.title.toLowerCase().includes(term) ||
    product.shortDescription.toLowerCase().includes(term) ||
    product.category.toLowerCase().includes(term) ||
    product.metadata.tags?.some(tag => tag.toLowerCase().includes(term))
  )
}

export function sortProducts(products: DynamicProduct[], sortBy: "name" | "date" | "category", order: "asc" | "desc"): DynamicProduct[] {
  return [...products].sort((a, b) => {
    let comparison = 0
    
    switch (sortBy) {
      case "name":
        comparison = a.title.localeCompare(b.title)
        break
      case "date":
        comparison = a.createdAt.getTime() - b.createdAt.getTime()
        break
      case "category":
        comparison = a.category.localeCompare(b.category)
        break
    }
    
    return order === "desc" ? -comparison : comparison
  })
}
