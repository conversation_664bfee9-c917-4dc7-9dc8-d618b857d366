@echo off
echo 🚀 Deploying Alraya Store to Vercel...

REM Check if git is initialized
if not exist ".git" (
    echo 📦 Initializing Git repository...
    git init
    git branch -M main
)

REM Add all files
echo 📁 Adding files to Git...
git add .

REM Commit changes
echo 💾 Committing changes...
git commit -m "Deploy: Alraya Store Dynamic CMS System - ✨ Features: Complete Dynamic Product CMS, 13 Field Types with validation, Mobile-responsive admin interface, Real-time preview with interactive mode, Image upload system, Arabic RTL support - 🔧 Technical: Next.js 15 with App Router, TypeScript + Tailwind CSS, Vercel optimized build, Production ready"

REM Check if Vercel CLI is installed
vercel --version >nul 2>&1
if errorlevel 1 (
    echo 📦 Installing Vercel CLI...
    npm install -g vercel
)

REM Deploy to Vercel
echo 🚀 Deploying to Vercel...
vercel --prod

echo ✅ Deployment complete!
echo.
echo 🎉 Your Alraya Store is now live!
echo 📱 Admin Panel: https://your-domain.vercel.app/admin
echo 🏪 Store Front: https://your-domain.vercel.app
echo.
echo 🔧 Next Steps:
echo 1. Configure your custom domain in Vercel dashboard
echo 2. Set up environment variables if needed
echo 3. Start creating your product templates!

pause
