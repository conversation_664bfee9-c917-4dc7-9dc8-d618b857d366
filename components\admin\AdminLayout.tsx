"use client"

import { useState } from "react"
import { useRouter, usePathname } from "next/navigation"
import { Card, CardContent } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { 
  LayoutDashboard, 
  Package, 
  FolderOpen, 
  Settings, 
  BarChart3,
  Users,
  ShoppingCart,
  DollarSign,
  Menu,
  X,
  Plus,
  Search,
  Bell,
  User
} from "lucide-react"

interface AdminLayoutProps {
  children: React.ReactNode
  title?: string
  subtitle?: string
  actions?: React.ReactNode
}

interface NavItem {
  id: string
  label: string
  icon: React.ReactNode
  href: string
  badge?: number
  submenu?: NavItem[]
}

const navigationItems: NavItem[] = [
  {
    id: "dashboard",
    label: "لوحة التحكم",
    icon: <LayoutDashboard className="h-5 w-5" />,
    href: "/admin"
  },
  {
    id: "products",
    label: "إدارة المنتجات",
    icon: <Package className="h-5 w-5" />,
    href: "/admin/products",
    badge: 12,
    submenu: [
      {
        id: "products-list",
        label: "قائمة المنتجات",
        icon: <Package className="h-4 w-4" />,
        href: "/admin/products"
      },
      {
        id: "products-create",
        label: "إنشاء منتج جديد",
        icon: <Plus className="h-4 w-4" />,
        href: "/admin/products/create"
      }
    ]
  },
  {
    id: "templates",
    label: "قوالب المنتجات",
    icon: <FolderOpen className="h-5 w-5" />,
    href: "/admin/templates",
    badge: 5,
    submenu: [
      {
        id: "templates-list",
        label: "قائمة القوالب",
        icon: <FolderOpen className="h-4 w-4" />,
        href: "/admin/templates"
      },
      {
        id: "templates-create",
        label: "إنشاء قالب جديد",
        icon: <Plus className="h-4 w-4" />,
        href: "/admin/templates/create"
      }
    ]
  },
  {
    id: "orders",
    label: "الطلبات",
    icon: <ShoppingCart className="h-5 w-5" />,
    href: "/admin/orders",
    badge: 8
  },
  {
    id: "analytics",
    label: "التحليلات",
    icon: <BarChart3 className="h-5 w-5" />,
    href: "/admin/analytics"
  },
  {
    id: "customers",
    label: "العملاء",
    icon: <Users className="h-5 w-5" />,
    href: "/admin/customers"
  },
  {
    id: "settings",
    label: "الإعدادات",
    icon: <Settings className="h-5 w-5" />,
    href: "/admin/settings",
    submenu: [
      {
        id: "settings-general",
        label: "إعدادات عامة",
        icon: <Settings className="h-4 w-4" />,
        href: "/admin/settings/general"
      },
      {
        id: "settings-checkout",
        label: "إعدادات الدفع",
        icon: <DollarSign className="h-4 w-4" />,
        href: "/admin/settings/checkout"
      }
    ]
  }
]

export function AdminLayout({ children, title, subtitle, actions }: AdminLayoutProps) {
  const [isSidebarOpen, setIsSidebarOpen] = useState(false)
  const [expandedItems, setExpandedItems] = useState<string[]>([])
  const router = useRouter()
  const pathname = usePathname()

  const toggleSidebar = () => {
    setIsSidebarOpen(!isSidebarOpen)
  }

  const toggleSubmenu = (itemId: string) => {
    setExpandedItems(prev => 
      prev.includes(itemId) 
        ? prev.filter(id => id !== itemId)
        : [...prev, itemId]
    )
  }

  const isActiveRoute = (href: string) => {
    return pathname === href || pathname.startsWith(href + "/")
  }

  const handleNavigation = (href: string) => {
    router.push(href)
    setIsSidebarOpen(false) // Close sidebar on mobile after navigation
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900 text-white">
      {/* Background Effects */}
      <div className="fixed inset-0 overflow-hidden pointer-events-none">
        <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-yellow-400/5 rounded-full blur-3xl" />
        <div className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-orange-500/5 rounded-full blur-3xl" />
      </div>

      {/* Mobile Sidebar Overlay */}
      {isSidebarOpen && (
        <div 
          className="fixed inset-0 bg-black/50 z-40 lg:hidden"
          onClick={() => setIsSidebarOpen(false)}
        />
      )}

      {/* Sidebar */}
      <aside className={`
        fixed top-0 right-0 h-full w-80 bg-slate-800/90 backdrop-blur-xl border-l border-slate-700/50 z-50 transform transition-transform duration-300 ease-in-out
        ${isSidebarOpen ? 'translate-x-0' : 'translate-x-full'}
        lg:translate-x-0 lg:static lg:z-auto
      `}>
        <div className="flex flex-col h-full">
          {/* Sidebar Header */}
          <div className="p-6 border-b border-slate-700/50">
            <div className="flex items-center justify-between">
              <div>
                <h2 className="text-xl font-bold bg-gradient-to-r from-yellow-400 to-orange-500 bg-clip-text text-transparent">
                  لوحة التحكم
                </h2>
                <p className="text-slate-400 text-sm">إدارة المتجر</p>
              </div>
              <Button
                variant="ghost"
                size="icon"
                onClick={toggleSidebar}
                className="lg:hidden text-slate-400 hover:text-white"
              >
                <X className="h-5 w-5" />
              </Button>
            </div>
          </div>

          {/* Navigation */}
          <nav className="flex-1 p-4 space-y-2 overflow-y-auto">
            {navigationItems.map((item) => (
              <div key={item.id}>
                <Button
                  variant="ghost"
                  onClick={() => {
                    if (item.submenu) {
                      toggleSubmenu(item.id)
                    } else {
                      handleNavigation(item.href)
                    }
                  }}
                  className={`
                    w-full justify-between text-right h-12 px-4
                    ${isActiveRoute(item.href) 
                      ? 'bg-gradient-to-r from-yellow-400/20 to-orange-500/20 text-yellow-400 border-r-2 border-yellow-400' 
                      : 'text-slate-300 hover:text-white hover:bg-slate-700/50'
                    }
                  `}
                >
                  <div className="flex items-center gap-3">
                    {item.icon}
                    <span className="font-medium">{item.label}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    {item.badge && (
                      <Badge className="bg-yellow-400 text-slate-900 text-xs">
                        {item.badge}
                      </Badge>
                    )}
                  </div>
                </Button>

                {/* Submenu */}
                {item.submenu && expandedItems.includes(item.id) && (
                  <div className="mr-4 mt-2 space-y-1">
                    {item.submenu.map((subItem) => (
                      <Button
                        key={subItem.id}
                        variant="ghost"
                        onClick={() => handleNavigation(subItem.href)}
                        className={`
                          w-full justify-start text-right h-10 px-4
                          ${isActiveRoute(subItem.href)
                            ? 'bg-yellow-400/10 text-yellow-400'
                            : 'text-slate-400 hover:text-white hover:bg-slate-700/30'
                          }
                        `}
                      >
                        <div className="flex items-center gap-3">
                          {subItem.icon}
                          <span className="text-sm">{subItem.label}</span>
                        </div>
                      </Button>
                    ))}
                  </div>
                )}
              </div>
            ))}
          </nav>

          {/* Sidebar Footer */}
          <div className="p-4 border-t border-slate-700/50">
            <div className="flex items-center gap-3 p-3 bg-slate-700/30 rounded-xl">
              <div className="w-10 h-10 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-full flex items-center justify-center">
                <User className="h-5 w-5 text-slate-900" />
              </div>
              <div className="flex-1">
                <p className="text-white font-medium text-sm">المدير</p>
                <p className="text-slate-400 text-xs"><EMAIL></p>
              </div>
            </div>
          </div>
        </div>
      </aside>

      {/* Main Content */}
      <div className="lg:mr-80">
        {/* Top Header */}
        <header className="sticky top-0 z-30 bg-slate-800/90 backdrop-blur-xl border-b border-slate-700/50">
          <div className="flex items-center justify-between p-4 lg:p-6">
            <div className="flex items-center gap-4">
              <Button
                variant="ghost"
                size="icon"
                onClick={toggleSidebar}
                className="lg:hidden text-slate-400 hover:text-white"
              >
                <Menu className="h-5 w-5" />
              </Button>
              
              <div>
                {title && (
                  <h1 className="text-xl lg:text-2xl font-bold text-white">
                    {title}
                  </h1>
                )}
                {subtitle && (
                  <p className="text-slate-400 text-sm lg:text-base">
                    {subtitle}
                  </p>
                )}
              </div>
            </div>

            <div className="flex items-center gap-3">
              {actions}
              
              <Button
                variant="ghost"
                size="icon"
                className="text-slate-400 hover:text-white relative"
              >
                <Bell className="h-5 w-5" />
                <span className="absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full"></span>
              </Button>
            </div>
          </div>
        </header>

        {/* Page Content */}
        <main className="relative z-10 p-4 lg:p-6">
          {children}
        </main>
      </div>
    </div>
  )
}
