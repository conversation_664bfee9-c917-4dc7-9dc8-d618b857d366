"use client"

import { useState } from "react"
import { use<PERSON><PERSON><PERSON> } from "next/navigation"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { AdminLayout } from "@/components/admin/AdminLayout"
import { mockCMSProducts } from "@/lib/data/mockCMSData"
import { DynamicProduct } from "@/lib/types"
import { filterProducts, sortProducts } from "@/lib/utils/cms"
import { 
  Plus, 
  Package, 
  Edit, 
  Copy, 
  Trash2, 
  Eye,
  EyeOff,
  Search,
  Filter,
  Star,
  Tag,
  Calendar,
  TrendingUp,
  BarChart3
} from "lucide-react"

export default function ProductsPage() {
  const router = useRouter()
  const [products, setProducts] = useState<DynamicProduct[]>(mockCMSProducts)
  const [searchQuery, setSearchQuery] = useState("")
  const [filterStatus, setFilterStatus] = useState<"all" | "active" | "inactive">("all")
  const [sortBy, setSortBy] = useState<"name" | "date" | "category">("date")
  const [sortOrder, setSortOrder] = useState<"asc" | "desc">("desc")

  const handleCreateProduct = () => {
    router.push("/admin/products/create")
  }

  const handleEditProduct = (product: DynamicProduct) => {
    router.push(`/admin/products/${product.id}/edit`)
  }

  const handleDuplicateProduct = (product: DynamicProduct) => {
    const duplicatedProduct: DynamicProduct = {
      ...product,
      id: `product_${Date.now()}`,
      slug: `${product.slug}-copy`,
      title: `${product.title} - نسخة`,
      isActive: false,
      createdAt: new Date(),
      updatedAt: new Date()
    }
    setProducts(prev => [...prev, duplicatedProduct])
  }

  const handleDeleteProduct = (productId: string) => {
    if (confirm("هل أنت متأكد من حذف هذا المنتج؟")) {
      setProducts(prev => prev.filter(p => p.id !== productId))
    }
  }

  const handleToggleActive = (productId: string) => {
    setProducts(prev => prev.map(product =>
      product.id === productId 
        ? { ...product, isActive: !product.isActive, updatedAt: new Date() }
        : product
    ))
  }

  const handleToggleStock = (productId: string) => {
    setProducts(prev => prev.map(product =>
      product.id === productId 
        ? { ...product, inStock: !product.inStock, updatedAt: new Date() }
        : product
    ))
  }

  // Filter and sort products
  const filteredProducts = filterProducts(products, searchQuery)
    .filter(product => {
      if (filterStatus === "active") return product.isActive
      if (filterStatus === "inactive") return !product.isActive
      return true
    })

  const sortedProducts = sortProducts(filteredProducts, sortBy, sortOrder)

  const stats = {
    total: products.length,
    active: products.filter(p => p.isActive).length,
    inactive: products.filter(p => !p.isActive).length,
    outOfStock: products.filter(p => !p.inStock).length,
    featured: products.filter(p => p.metadata.featured).length,
    popular: products.filter(p => p.metadata.popular).length
  }

  return (
    <AdminLayout 
      title="إدارة المنتجات" 
      subtitle={`إدارة جميع منتجات المتجر (${products.length} منتج)`}
      actions={
        <Button 
          onClick={handleCreateProduct}
          className="bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white font-bold"
        >
          <Plus className="h-4 w-4 ml-2" />
          منتج جديد
        </Button>
      }
    >
      <div className="space-y-6">
        {/* Products Stats */}
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
          <Card className="bg-slate-800/50 border-slate-700/50 backdrop-blur-sm">
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-slate-400 text-sm">إجمالي</p>
                  <p className="text-xl font-bold text-white">{stats.total}</p>
                </div>
                <Package className="h-6 w-6 text-blue-400" />
              </div>
            </CardContent>
          </Card>

          <Card className="bg-slate-800/50 border-slate-700/50 backdrop-blur-sm">
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-slate-400 text-sm">نشط</p>
                  <p className="text-xl font-bold text-green-400">{stats.active}</p>
                </div>
                <Eye className="h-6 w-6 text-green-400" />
              </div>
            </CardContent>
          </Card>

          <Card className="bg-slate-800/50 border-slate-700/50 backdrop-blur-sm">
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-slate-400 text-sm">غير نشط</p>
                  <p className="text-xl font-bold text-slate-400">{stats.inactive}</p>
                </div>
                <EyeOff className="h-6 w-6 text-slate-400" />
              </div>
            </CardContent>
          </Card>

          <Card className="bg-slate-800/50 border-slate-700/50 backdrop-blur-sm">
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-slate-400 text-sm">نفد المخزون</p>
                  <p className="text-xl font-bold text-red-400">{stats.outOfStock}</p>
                </div>
                <Package className="h-6 w-6 text-red-400" />
              </div>
            </CardContent>
          </Card>

          <Card className="bg-slate-800/50 border-slate-700/50 backdrop-blur-sm">
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-slate-400 text-sm">مميز</p>
                  <p className="text-xl font-bold text-yellow-400">{stats.featured}</p>
                </div>
                <Star className="h-6 w-6 text-yellow-400" />
              </div>
            </CardContent>
          </Card>

          <Card className="bg-slate-800/50 border-slate-700/50 backdrop-blur-sm">
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-slate-400 text-sm">الأكثر طلباً</p>
                  <p className="text-xl font-bold text-orange-400">{stats.popular}</p>
                </div>
                <TrendingUp className="h-6 w-6 text-orange-400" />
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Search and Filters */}
        <Card className="bg-slate-800/50 border-slate-700/50 backdrop-blur-sm">
          <CardContent className="p-4">
            <div className="flex flex-col md:flex-row gap-4">
              {/* Search */}
              <div className="flex-1 relative">
                <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-slate-400" />
                <Input
                  placeholder="البحث في المنتجات..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="bg-slate-700 border-slate-600 text-white pr-10"
                />
              </div>

              {/* Status Filter */}
              <select
                value={filterStatus}
                onChange={(e) => setFilterStatus(e.target.value as any)}
                className="px-3 py-2 bg-slate-700 border border-slate-600 rounded text-white"
              >
                <option value="all">جميع المنتجات</option>
                <option value="active">المنتجات النشطة</option>
                <option value="inactive">المنتجات غير النشطة</option>
              </select>

              {/* Sort */}
              <select
                value={`${sortBy}-${sortOrder}`}
                onChange={(e) => {
                  const [newSortBy, newSortOrder] = e.target.value.split('-')
                  setSortBy(newSortBy as any)
                  setSortOrder(newSortOrder as any)
                }}
                className="px-3 py-2 bg-slate-700 border border-slate-600 rounded text-white"
              >
                <option value="date-desc">الأحدث أولاً</option>
                <option value="date-asc">الأقدم أولاً</option>
                <option value="name-asc">الاسم (أ-ي)</option>
                <option value="name-desc">الاسم (ي-أ)</option>
                <option value="category-asc">الفئة (أ-ي)</option>
                <option value="category-desc">الفئة (ي-أ)</option>
              </select>
            </div>
          </CardContent>
        </Card>

        {/* Products Grid */}
        {sortedProducts.length === 0 ? (
          <Card className="bg-slate-800/50 border-slate-700/50 backdrop-blur-sm">
            <CardContent className="p-12 text-center">
              <div className="text-6xl mb-4">📦</div>
              <h3 className="text-xl font-bold text-white mb-2">
                {searchQuery ? "لا توجد نتائج" : "لا توجد منتجات بعد"}
              </h3>
              <p className="text-slate-400 mb-6">
                {searchQuery 
                  ? `لم نجد أي منتجات تطابق "${searchQuery}"`
                  : "ابدأ بإنشاء منتج جديد لمتجرك"
                }
              </p>
              {!searchQuery && (
                <Button 
                  onClick={handleCreateProduct}
                  className="bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700"
                >
                  <Plus className="h-4 w-4 ml-2" />
                  إنشاء أول منتج
                </Button>
              )}
            </CardContent>
          </Card>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {sortedProducts.map((product) => (
              <Card 
                key={product.id} 
                className="bg-slate-800/50 border-slate-700/50 backdrop-blur-sm hover:bg-slate-700/50 transition-all duration-300 group"
              >
                <CardHeader className="pb-3">
                  <div className="flex items-start justify-between">
                    <div className="flex items-center gap-3">
                      <div className="w-12 h-12 bg-slate-700 rounded-lg flex items-center justify-center overflow-hidden">
                        {product.image ? (
                          <img 
                            src={product.image} 
                            alt={product.title}
                            className="w-full h-full object-cover"
                            onError={(e) => {
                              e.currentTarget.style.display = 'none'
                              e.currentTarget.nextElementSibling?.classList.remove('hidden')
                            }}
                          />
                        ) : (
                          <Package className="h-6 w-6 text-slate-400" />
                        )}
                        <div className="hidden">
                          <Package className="h-6 w-6 text-slate-400" />
                        </div>
                      </div>
                      
                      <div className="flex-1 min-w-0">
                        <CardTitle className="text-white text-lg truncate">{product.title}</CardTitle>
                        <p className="text-slate-400 text-sm truncate">{product.shortDescription}</p>
                      </div>
                    </div>
                  </div>
                </CardHeader>

                <CardContent className="space-y-4">
                  {/* Product Info */}
                  <div className="space-y-2">
                    <div className="flex items-center justify-between text-sm">
                      <span className="text-slate-400">الفئة:</span>
                      <Badge className="bg-slate-700 text-slate-300 text-xs">
                        {product.category}
                      </Badge>
                    </div>
                    
                    <div className="flex items-center justify-between text-sm">
                      <span className="text-slate-400">الحقول:</span>
                      <span className="text-white">{product.fields.length}</span>
                    </div>

                    {product.rating && (
                      <div className="flex items-center justify-between text-sm">
                        <span className="text-slate-400">التقييم:</span>
                        <div className="flex items-center gap-1">
                          <Star className="h-3 w-3 text-yellow-400 fill-current" />
                          <span className="text-white">{product.rating}</span>
                          {product.reviewsCount && (
                            <span className="text-slate-400">({product.reviewsCount})</span>
                          )}
                        </div>
                      </div>
                    )}
                  </div>

                  {/* Status Badges */}
                  <div className="flex flex-wrap gap-2">
                    <Badge 
                      className={product.isActive 
                        ? "bg-green-500/20 text-green-400" 
                        : "bg-slate-500/20 text-slate-400"
                      }
                    >
                      {product.isActive ? "نشط" : "غير نشط"}
                    </Badge>

                    <Badge 
                      className={product.inStock 
                        ? "bg-blue-500/20 text-blue-400" 
                        : "bg-red-500/20 text-red-400"
                      }
                    >
                      {product.inStock ? "متوفر" : "نفد المخزون"}
                    </Badge>

                    {product.metadata.featured && (
                      <Badge className="bg-yellow-500/20 text-yellow-400">
                        مميز
                      </Badge>
                    )}

                    {product.metadata.popular && (
                      <Badge className="bg-orange-500/20 text-orange-400">
                        الأكثر طلباً
                      </Badge>
                    )}
                  </div>

                  {/* Dates */}
                  <div className="text-xs text-slate-400 space-y-1">
                    <p>تم الإنشاء: {new Date(product.createdAt).toLocaleDateString('ar-EG')}</p>
                    <p>آخر تحديث: {new Date(product.updatedAt).toLocaleDateString('ar-EG')}</p>
                  </div>

                  {/* Actions */}
                  <div className="grid grid-cols-2 gap-2 pt-2 border-t border-slate-700">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleEditProduct(product)}
                      className="text-blue-400 hover:text-blue-300"
                    >
                      <Edit className="h-4 w-4 mr-1" />
                      تعديل
                    </Button>

                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleToggleActive(product.id)}
                      className={product.isActive ? "text-red-400 hover:text-red-300" : "text-green-400 hover:text-green-300"}
                    >
                      {product.isActive ? <EyeOff className="h-4 w-4 mr-1" /> : <Eye className="h-4 w-4 mr-1" />}
                      {product.isActive ? "إلغاء تفعيل" : "تفعيل"}
                    </Button>

                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleDuplicateProduct(product)}
                      className="text-green-400 hover:text-green-300"
                    >
                      <Copy className="h-4 w-4 mr-1" />
                      نسخ
                    </Button>

                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleDeleteProduct(product.id)}
                      className="text-red-400 hover:text-red-300"
                    >
                      <Trash2 className="h-4 w-4 mr-1" />
                      حذف
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        )}

        {/* Results Summary */}
        {sortedProducts.length > 0 && (
          <div className="text-center text-slate-400 text-sm">
            عرض {sortedProducts.length} من أصل {products.length} منتج
            {searchQuery && ` • البحث: "${searchQuery}"`}
            {filterStatus !== "all" && ` • الفلتر: ${filterStatus === "active" ? "نشط" : "غير نشط"}`}
          </div>
        )}
      </div>
    </AdminLayout>
  )
}
