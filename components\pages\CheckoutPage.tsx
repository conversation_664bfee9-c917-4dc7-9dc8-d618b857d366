"use client"

import { useState } from "react"
import { useRouter } from "next/navigation"
import { AppHeader } from "@/components/layout/AppHeader"
import { SideMenu } from "@/components/layout/SideMenu"
import { NewsTicket } from "@/components/shared/NewsTicket"
import { MobileNavigation } from "@/components/layout/MobileNavigation"
import { DesktopFooter } from "@/components/layout/DesktopFooter"
import { CheckoutProvider, useCheckout } from "@/components/checkout/CheckoutContext"
import { ProgressStepper } from "@/components/checkout/ProgressStepper"
import { Step1Details } from "@/components/checkout/Step1Details"
import { Step2OrderSummary } from "@/components/checkout/Step2OrderSummary"
import { Step3Payment } from "@/components/checkout/Step3Payment"
import { generateOrderId } from "@/lib/utils/generateOrderId"
import { saveCheckoutOrder } from "@/lib/utils/localStorage"
import { CheckoutOrder } from "@/lib/types"
import { ShoppingCart } from "lucide-react"

function CheckoutContent() {
  const { state, setStep, resetCheckout } = useCheckout()
  const router = useRouter()

  const handleStepNext = () => {
    if (state.step < 3) {
      setStep((state.step + 1) as 1 | 2 | 3)
    }
  }

  const handleStepPrevious = () => {
    if (state.step > 1) {
      setStep((state.step - 1) as 1 | 2 | 3)
    }
  }

  const handleSubmitOrder = async () => {
    if (!state.userDetails || !state.selectedBank) return

    try {
      // Generate order ID
      const orderId = generateOrderId()

      // Create order object
      const order: CheckoutOrder = {
        id: orderId,
        amount: state.amount,
        currency: state.currency,
        userDetails: state.userDetails,
        selectedBank: state.selectedBank,
        referenceNumber: state.referenceNumber,
        receiptFileName: state.receiptFile?.name,
        status: "pending",
        createdAt: new Date()
      }

      // Save order to localStorage
      saveCheckoutOrder(order)

      // Reset checkout state
      resetCheckout()

      // Navigate to success page with order ID
      router.push(`/checkout/success?orderId=${orderId}`)
    } catch (error) {
      console.error("Error submitting order:", error)
      // TODO: Show error message to user
    }
  }

  const renderCurrentStep = () => {
    switch (state.step) {
      case 1:
        return <Step1Details onNext={handleStepNext} />
      case 2:
        return (
          <Step2OrderSummary 
            onNext={handleStepNext} 
            onPrevious={handleStepPrevious} 
          />
        )
      case 3:
        return (
          <Step3Payment 
            onPrevious={handleStepPrevious} 
            onSubmit={handleSubmitOrder} 
          />
        )
      default:
        return <Step1Details onNext={handleStepNext} />
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900 text-white relative overflow-hidden">
      {/* Background Effects */}
      <div className="absolute inset-0 bg-[radial-gradient(ellipse_at_top,_var(--tw-gradient-stops))] from-yellow-400/20 via-transparent to-transparent" />
      <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-yellow-400/10 rounded-full blur-3xl" />
      <div className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-orange-500/10 rounded-full blur-3xl" />

      {/* Main Content */}
      <main className="relative z-10 p-4 lg:p-8 space-y-6 lg:space-y-8 pb-32 pt-32 lg:pt-36 max-w-7xl mx-auto">
        {/* Page Header */}
        <div className="text-center space-y-4">
          <div className="flex items-center justify-center gap-4 mb-4">
            <div className="p-3 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-2xl shadow-lg">
              <ShoppingCart className="h-8 w-8 text-slate-900" />
            </div>
          </div>
          <h1 className="text-3xl lg:text-4xl font-bold bg-gradient-to-r from-yellow-400 to-orange-500 bg-clip-text text-transparent">
            شحن المحفظة
          </h1>
          <p className="text-slate-300 text-base lg:text-lg max-w-md mx-auto">
            اشحن محفظتك بسهولة وأمان في خطوات بسيطة
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
          {/* Progress Stepper - Desktop Sidebar */}
          <div className="lg:col-span-1">
            <div className="lg:sticky lg:top-8">
              <ProgressStepper currentStep={state.step} />
            </div>
          </div>

          {/* Main Content Area */}
          <div className="lg:col-span-3">
            {renderCurrentStep()}
          </div>
        </div>
      </main>
    </div>
  )
}

export function CheckoutPage() {
  const [activeTab, setActiveTab] = useState("checkout")
  const [isMenuOpen, setIsMenuOpen] = useState(false)
  const router = useRouter()

  // Navigation handler for navbar
  const handleTabChange = (tab: string) => {
    if (tab === "wallet") {
      router.push("/wallet")
    } else if (tab === "profile") {
      router.push("/profile")
    } else if (tab === "shop") {
      router.push("/shop")
    } else if (tab === "home") {
      router.push("/")
      router.refresh()
    } else {
      setActiveTab(tab)
    }
  }

  return (
    <CheckoutProvider>
      <div className="min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900 text-white relative overflow-hidden">
        {/* Background Effects */}
        <div className="absolute inset-0 bg-[radial-gradient(ellipse_at_top,_var(--tw-gradient-stops))] from-yellow-400/20 via-transparent to-transparent" />
        <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-yellow-400/10 rounded-full blur-3xl" />
        <div className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-orange-500/10 rounded-full blur-3xl" />

        <AppHeader onMenuOpen={() => setIsMenuOpen(true)} />
        <NewsTicket />
        <SideMenu isOpen={isMenuOpen} onClose={() => setIsMenuOpen(false)} />

        <CheckoutContent />

        <MobileNavigation activeTab={activeTab} onTabChange={handleTabChange} />
        <DesktopFooter activeTab={activeTab} onTabChange={handleTabChange} />
      </div>
    </CheckoutProvider>
  )
}
