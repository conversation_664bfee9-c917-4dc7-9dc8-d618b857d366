import { notFound } from "next/navigation"
import { ProductStandard } from "@/components/products/ProductStandard"
import { ProductInstant } from "@/components/products/ProductInstant"
import { ProductPageWrapper } from "@/components/products/ProductPageWrapper"
import { realisticProducts } from "@/lib/data/realisticProducts"

// Convert realistic products to the format expected by product components
function convertToProductFormat(product: any) {
  return {
    id: product.id,
    flow: product.type === "instant" ? "instant" as const : "standard" as const,
    title: product.title,
    shortDescription: product.shortDescription,
    image: product.image,
    category: product.category,
    rating: product.rating,
    reviewsCount: product.reviewsCount,
    estimatedTime: product.estimatedTime,
    packs: product.packages?.map((pkg: any) => ({
      id: pkg.id,
      name: pkg.name,
      amount: pkg.amount,
      price: pkg.price,
      originalPrice: pkg.originalPrice,
      discount: pkg.discount,
      popular: pkg.popular
    })) || [],
    servers: [
      { id: "global", name: "Global", region: "عالمي" },
      { id: "mena", name: "MENA", region: "الشرق الأوسط وشمال أفريقيا" }
    ],
    inStock: true,
    description: product.shortDescription,
    instructions: [
      "تأكد من إدخال البيانات بشكل صحيح",
      "اختر الحزمة المناسبة لاحتياجاتك",
      "تأكد من أن حسابك نشط ومتاح للشحن",
      "لا تشارك معلومات حسابك مع أي شخص آخر",
      "في حالة وجود مشكلة، تواصل مع الدعم الفني فوراً"
    ],
    features: product.features || [
      "شحن سريع وآمن",
      "دعم فني متخصص",
      "أسعار تنافسية",
      "ضمان الجودة"
    ]
  }
}

// Create mock products from realistic products
const mockProductsFromRealistic = realisticProducts.reduce((acc, product) => {
  acc[product.id] = convertToProductFormat(product)
  return acc
}, {} as Record<string, any>)

// ## Mock product data - will be replaced with Supabase queries
const mockProducts = {
  "pubg-mobile-uc": {
    id: "pubg-mobile-uc",
    flow: "instant" as const,
    title: "شحن يوسي PUBG Mobile",
    shortDescription: "شحن فوري لعملة UC في لعبة PUBG Mobile - احصل على يوسي فوراً",
    image: "https://c4.wallpaperflare.com/wallpaper/1003/987/595/pubg-player-unknown-battleground-players-hd-wallpaper-preview.jpg",
    category: "ألعاب الموبايل",
    rating: 4.8,
    reviewsCount: 1250,
    estimatedTime: "أقل من دقيقة",
    packs: [
      {
        id: "uc-60",
        name: "60 UC",
        amount: "60 يوسي",
        price: 25,
        originalPrice: 30,
        discount: 17
      },
      {
        id: "uc-325",
        name: "325 UC",
        amount: "325 يوسي",
        price: 120,
        originalPrice: 150,
        discount: 20,
        popular: true
      },
      {
        id: "uc-660",
        name: "660 UC",
        amount: "660 يوسي",
        price: 240,
        originalPrice: 300,
        discount: 20
      },
      {
        id: "uc-1800",
        name: "1800 UC",
        amount: "1800 يوسي",
        price: 600,
        originalPrice: 750,
        discount: 20
      },
      {
        id: "uc-3850",
        name: "3850 UC",
        amount: "3850 يوسي",
        price: 1200,
        originalPrice: 1500,
        discount: 20
      }
    ],
    servers: [
      { id: "global", name: "Global", region: "عالمي" },
      { id: "korea", name: "Korea", region: "آسيا" },
      { id: "vietnam", name: "Vietnam", region: "آسيا" }
    ],
    inStock: true,
    description: "شحن فوري لعملة UC في لعبة PUBG Mobile. احصل على يوسي فوراً واستمتع بشراء الأسلحة والملابس والعناصر المميزة في اللعبة.",
    instructions: [
      "تأكد من إدخال الأيدي الخاص بك بشكل صحيح",
      "اختر السيرفر المناسب لحسابك",
      "تأكد من أن حسابك نشط ومتاح للشحن",
      "لا تشارك معلومات حسابك مع أي شخص آخر",
      "في حالة وجود مشكلة، تواصل مع الدعم الفني فوراً"
    ],
    features: [
      "شحن فوري خلال أقل من دقيقة",
      "دعم جميع السيرفرات العالمية",
      "أسعار تنافسية ومميزة",
      "ضمان الجودة والأمان",
      "دعم فني متاح 24/7"
    ]
  },
  "free-fire-diamonds": {
    id: "free-fire-diamonds",
    flow: "instant" as const,
    title: "شحن جواهر Free Fire",
    shortDescription: "شحن فوري للجواهر في لعبة Free Fire - احصل على الجواهر فوراً",
    image: "https://c4.wallpaperflare.com/wallpaper/1003/987/595/pubg-player-unknown-battleground-players-hd-wallpaper-preview.jpg",
    category: "ألعاب الموبايل",
    rating: 4.7,
    reviewsCount: 980,
    estimatedTime: "أقل من دقيقة",
    packs: [
      {
        id: "diamonds-100",
        name: "100 Diamonds",
        amount: "100 جوهرة",
        price: 30,
        originalPrice: 35,
        discount: 14
      },
      {
        id: "diamonds-310",
        name: "310 Diamonds",
        amount: "310 جوهرة",
        price: 90,
        originalPrice: 110,
        discount: 18,
        popular: true
      },
      {
        id: "diamonds-520",
        name: "520 Diamonds",
        amount: "520 جوهرة",
        price: 150,
        originalPrice: 180,
        discount: 17
      }
    ],
    servers: [
      { id: "global", name: "Global", region: "عالمي" },
      { id: "indonesia", name: "Indonesia", region: "آسيا" },
      { id: "brazil", name: "Brazil", region: "أمريكا الجنوبية" }
    ],
    inStock: true,
    description: "شحن فوري للجواهر في لعبة Free Fire. احصل على الجواهر واستمتع بشراء الشخصيات والأسلحة والملابس المميزة.",
    instructions: [
      "تأكد من إدخال الأيدي الخاص بك بشكل صحيح",
      "اختر السيرفر المناسب لمنطقتك",
      "تأكد من أن حسابك نشط ومتاح للشحن",
      "لا تشارك معلومات حسابك مع أي شخص آخر",
      "في حالة وجود مشكلة، تواصل مع الدعم الفني فوراً"
    ],
    features: [
      "شحن فوري خلال أقل من دقيقة",
      "دعم جميع المناطق",
      "أسعار مميزة وتنافسية",
      "ضمان الأمان والجودة",
      "خدمة عملاء ممتازة"
    ]
  },
  "steam-wallet": {
    id: "steam-wallet",
    flow: "standard" as const,
    title: "بطاقة Steam Wallet",
    shortDescription: "بطاقات شحن Steam للألعاب والمحتوى الرقمي",
    image: "https://c4.wallpaperflare.com/wallpaper/1003/987/595/pubg-player-unknown-battleground-players-hd-wallpaper-preview.jpg",
    category: "بطاقات الألعاب",
    rating: 4.9,
    reviewsCount: 2100,
    estimatedTime: "فوري",
    offers: [
      {
        id: "steam-250",
        name: "250 ج.س",
        amount: "250 جنيه سوداني",
        price: 250,
        originalPrice: 280,
        discount: 11
      },
      {
        id: "steam-500",
        name: "500 ج.س",
        amount: "500 جنيه سوداني",
        price: 500,
        originalPrice: 560,
        discount: 11,
        popular: true
      },
      {
        id: "steam-1000",
        name: "1000 ج.س",
        amount: "1000 جنيه سوداني",
        price: 1000,
        originalPrice: 1120,
        discount: 11
      }
    ],
    regions: [
      { id: "global", name: "عالمي" },
      { id: "us", name: "الولايات المتحدة" },
      { id: "eu", name: "أوروبا" }
    ],
    inStock: true,
    description: "بطاقات شحن Steam Wallet الأصلية. اشحن محفظتك واستمتع بشراء الألعاب والمحتوى الرقمي من متجر Steam.",
    features: [
      "تفعيل فوري للبطاقة",
      "صالحة عالمياً",
      "دعم فني متاح",
      "ضمان الأصالة",
      "أسعار تنافسية"
    ]
  },
  "google-play": {
    id: "google-play",
    flow: "standard" as const,
    title: "بطاقة Google Play",
    shortDescription: "بطاقات شحن Google Play للتطبيقات والألعاب",
    image: "https://c4.wallpaperflare.com/wallpaper/1003/987/595/pubg-player-unknown-battleground-players-hd-wallpaper-preview.jpg",
    category: "بطاقات الألعاب",
    rating: 4.8,
    reviewsCount: 1580,
    estimatedTime: "فوري",
    offers: [
      {
        id: "gplay-500",
        name: "500 ج.س",
        amount: "500 جنيه سوداني",
        price: 500,
        originalPrice: 550,
        discount: 9
      },
      {
        id: "gplay-1000",
        name: "1000 ج.س",
        amount: "1000 جنيه سوداني",
        price: 1000,
        originalPrice: 1100,
        discount: 9,
        popular: true
      },
      {
        id: "gplay-2000",
        name: "2000 ج.س",
        amount: "2000 جنيه سوداني",
        price: 2000,
        originalPrice: 2200,
        discount: 9
      }
    ],
    regions: [
      { id: "global", name: "عالمي" },
      { id: "mena", name: "الشرق الأوسط وشمال أفريقيا" }
    ],
    inStock: true,
    description: "بطاقات شحن Google Play الأصلية. اشحن حسابك واستمتع بشراء التطبيقات والألعاب والمحتوى الرقمي.",
    features: [
      "تفعيل فوري للبطاقة",
      "صالحة لجميع التطبيقات",
      "دعم فني متاح",
      "ضمان الأصالة",
      "أسعار مميزة"
    ]
  }
}

interface ProductPageProps {
  params: Promise<{
    slug: string
  }>
}

export default async function ProductPage({ params }: ProductPageProps) {
  // ## Get product data - will be replaced with Supabase query
  const { slug } = await params

  // Try to find product in realistic products first, then fallback to mock products
  let product = mockProductsFromRealistic[slug]
  if (!product) {
    product = mockProducts[slug as keyof typeof mockProducts]
  }

  if (!product) {
    notFound()
  }

  // Render appropriate component based on product flow
  if (product.flow === "instant") {
    return (
      <ProductPageWrapper>
        <ProductInstant
          product={product}
        />
      </ProductPageWrapper>
    )
  }

  return (
    <ProductPageWrapper>
      <ProductStandard
        product={product}
      />
    </ProductPageWrapper>
  )
}

// ## Generate static params for known products - will be replaced with Supabase query
export async function generateStaticParams() {
  const allSlugs = [
    ...Object.keys(mockProducts),
    ...Object.keys(mockProductsFromRealistic)
  ]

  return allSlugs.map((slug) => ({
    slug,
  }))
}
