"use client"

import React, { useState } from "react"
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { 
  ShoppingCart,
  Star,
  Plus,
  Minus,
  AlertCircle,
  CheckCircle
} from "lucide-react"
import { ProductTemplate, DynamicField } from "@/lib/types"
import { validateField, validateForm } from "@/lib/validation"
import { ImageFieldComponent } from "./ImageFieldComponent"

interface InteractiveProductFormProps {
  template: ProductTemplate
  onSubmit?: (formData: Record<string, any>) => void
}

export function InteractiveProductForm({ template, onSubmit }: InteractiveProductFormProps) {
  const [formData, setFormData] = useState<Record<string, any>>({})
  const [errors, setErrors] = useState<Record<string, string>>({})
  const [touched, setTouched] = useState<Record<string, boolean>>({})

  const handleFieldChange = (fieldName: string, value: any, field: DynamicField) => {
    // Update form data
    setFormData(prev => ({
      ...prev,
      [fieldName]: value
    }))

    // Mark field as touched
    setTouched(prev => ({
      ...prev,
      [fieldName]: true
    }))

    // Validate field
    const validation = validateField(field, value)
    setErrors(prev => ({
      ...prev,
      [fieldName]: validation.isValid ? "" : (validation.error || "")
    }))
  }

  const handleSubmit = () => {
    // Validate entire form
    const validation = validateForm(template.fields, formData)
    
    if (validation.isValid) {
      onSubmit?.(formData)
      alert("تم إرسال الطلب بنجاح!")
    } else {
      setErrors(validation.errors)
      // Mark all fields as touched to show errors
      const allTouched = template.fields.reduce((acc, field) => ({
        ...acc,
        [field.name]: true
      }), {})
      setTouched(allTouched)
    }
  }

  const renderField = (field: DynamicField) => {
    if (!field.visible) return null

    const fieldValue = formData[field.name]
    const fieldError = errors[field.name]
    const isFieldTouched = touched[field.name]
    const showError = isFieldTouched && fieldError

    const baseClasses = `bg-slate-700/50 border-slate-600 text-white ${
      showError ? "border-red-500" : ""
    }`

    switch (field.type) {
      case "heading":
        const HeadingTag = `h${field.level || 2}` as keyof JSX.IntrinsicElements
        return (
          <HeadingTag 
            key={field.id}
            className={`font-bold text-white mb-4 ${
              field.level === 1 ? "text-3xl" : 
              field.level === 2 ? "text-2xl" : 
              field.level === 3 ? "text-xl" : 
              field.level === 4 ? "text-lg" : 
              field.level === 5 ? "text-base" : "text-sm"
            } ${
              field.alignment === "center" ? "text-center" : 
              field.alignment === "right" ? "text-right" : "text-left"
            }`}
            style={{ color: field.color }}
          >
            {field.label}
          </HeadingTag>
        )

      case "divider":
        return (
          <div key={field.id} className="my-6">
            <div className="w-full h-px bg-gradient-to-r from-transparent via-slate-600 to-transparent" />
          </div>
        )

      case "text":
      case "email":
      case "number":
        return (
          <div key={field.id} className="space-y-2">
            <Label className="text-slate-300">
              {field.label}
              {field.required && <span className="text-red-400 mr-1">*</span>}
            </Label>
            <Input
              type={field.type === "number" ? "number" : field.type === "email" ? "email" : "text"}
              value={fieldValue || ""}
              onChange={(e) => handleFieldChange(field.name, e.target.value, field)}
              placeholder={field.placeholder}
              className={baseClasses}
            />
            {showError && (
              <Alert className="border-red-500 bg-red-500/10">
                <AlertCircle className="h-4 w-4" />
                <AlertDescription className="text-red-400">{fieldError}</AlertDescription>
              </Alert>
            )}
            {field.description && (
              <p className="text-sm text-slate-400">{field.description}</p>
            )}
          </div>
        )

      case "textarea":
        return (
          <div key={field.id} className="space-y-2">
            <Label className="text-slate-300">
              {field.label}
              {field.required && <span className="text-red-400 mr-1">*</span>}
            </Label>
            <Textarea
              value={fieldValue || ""}
              onChange={(e) => handleFieldChange(field.name, e.target.value, field)}
              placeholder={field.placeholder}
              className={baseClasses}
            />
            {showError && (
              <Alert className="border-red-500 bg-red-500/10">
                <AlertCircle className="h-4 w-4" />
                <AlertDescription className="text-red-400">{fieldError}</AlertDescription>
              </Alert>
            )}
            {field.description && (
              <p className="text-sm text-slate-400">{field.description}</p>
            )}
          </div>
        )

      case "select":
        return (
          <div key={field.id} className="space-y-2">
            <Label className="text-slate-300">
              {field.label}
              {field.required && <span className="text-red-400 mr-1">*</span>}
            </Label>
            <Select
              value={fieldValue || ""}
              onValueChange={(value) => handleFieldChange(field.name, value, field)}
            >
              <SelectTrigger className={baseClasses}>
                <SelectValue placeholder={field.placeholder || "اختر..."} />
              </SelectTrigger>
              <SelectContent>
                {field.options?.map((option) => (
                  <SelectItem key={option.id} value={option.value}>
                    <div className="flex items-center justify-between w-full">
                      <span>{option.label}</span>
                      {option.price && option.price > 0 && (
                        <span className="text-green-400 text-sm mr-2">+{option.price} ج.س.</span>
                      )}
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            {showError && (
              <Alert className="border-red-500 bg-red-500/10">
                <AlertCircle className="h-4 w-4" />
                <AlertDescription className="text-red-400">{fieldError}</AlertDescription>
              </Alert>
            )}
            {field.description && (
              <p className="text-sm text-slate-400">{field.description}</p>
            )}
          </div>
        )

      case "radio":
        return (
          <div key={field.id} className="space-y-2">
            <Label className="text-slate-300">
              {field.label}
              {field.required && <span className="text-red-400 mr-1">*</span>}
            </Label>
            <div className="space-y-2">
              {field.options?.map((option) => (
                <label key={option.id} className="flex items-center gap-2 cursor-pointer">
                  <input 
                    type="radio" 
                    name={field.name}
                    value={option.value}
                    checked={fieldValue === option.value}
                    onChange={(e) => handleFieldChange(field.name, e.target.value, field)}
                    className="text-blue-500 border-slate-600 bg-slate-700"
                  />
                  <span className="text-white">{option.label}</span>
                  {option.price && option.price > 0 && (
                    <span className="text-green-400 text-sm">+{option.price} ج.س.</span>
                  )}
                </label>
              ))}
            </div>
            {showError && (
              <Alert className="border-red-500 bg-red-500/10">
                <AlertCircle className="h-4 w-4" />
                <AlertDescription className="text-red-400">{fieldError}</AlertDescription>
              </Alert>
            )}
            {field.description && (
              <p className="text-sm text-slate-400">{field.description}</p>
            )}
          </div>
        )

      case "checkbox":
        return (
          <div key={field.id} className="space-y-2">
            <div className="flex items-center space-x-2 space-x-reverse">
              <input
                type="checkbox"
                checked={fieldValue || false}
                onChange={(e) => handleFieldChange(field.name, e.target.checked, field)}
                className="rounded border-slate-600 bg-slate-700"
              />
              <Label className="text-slate-300">
                {field.label}
                {field.required && <span className="text-red-400 mr-1">*</span>}
              </Label>
            </div>
            {showError && (
              <Alert className="border-red-500 bg-red-500/10">
                <AlertCircle className="h-4 w-4" />
                <AlertDescription className="text-red-400">{fieldError}</AlertDescription>
              </Alert>
            )}
          </div>
        )

      case "image":
        return (
          <div key={field.id} className="space-y-2">
            <ImageFieldComponent
              field={field}
              value={fieldValue}
              onChange={(value) => handleFieldChange(field.name, value, field)}
            />
            {showError && (
              <Alert className="border-red-500 bg-red-500/10">
                <AlertCircle className="h-4 w-4" />
                <AlertDescription className="text-red-400">{fieldError}</AlertDescription>
              </Alert>
            )}
          </div>
        )

      default:
        return (
          <div key={field.id} className="p-4 bg-slate-700/30 border border-slate-600 rounded-lg">
            <p className="text-slate-400">نوع حقل غير مدعوم: {field.type}</p>
          </div>
        )
    }
  }

  return (
    <Card className="bg-slate-800/50 border-slate-700/50 backdrop-blur-sm">
      <CardHeader>
        <div className="text-center space-y-4">
          <h1 className="text-2xl lg:text-3xl font-bold bg-gradient-to-r from-yellow-400 to-orange-500 bg-clip-text text-transparent">
            {template.name}
          </h1>
          {template.description && (
            <p className="text-slate-300">{template.description}</p>
          )}
          <Badge variant="secondary">{template.category}</Badge>
        </div>
      </CardHeader>
      
      <CardContent className="space-y-6">
        {/* Render all fields */}
        {template.fields
          .sort((a, b) => a.order - b.order)
          .map(renderField)}

        {/* Action Buttons */}
        <div className="flex flex-col sm:flex-row gap-3 pt-6 border-t border-slate-700">
          <Button 
            onClick={handleSubmit}
            className="flex-1 bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700"
          >
            <ShoppingCart className="h-4 w-4 ml-2" />
            إضافة إلى السلة
          </Button>
          <Button 
            variant="outline" 
            className="border-slate-600 text-slate-300"
          >
            <Star className="h-4 w-4 ml-2" />
            إضافة للمفضلة
          </Button>
        </div>
      </CardContent>
    </Card>
  )
}
