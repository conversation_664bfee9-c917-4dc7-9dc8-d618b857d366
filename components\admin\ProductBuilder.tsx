"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Badge } from "@/components/ui/badge"
import { FieldBuilder } from "./FieldBuilder"
import { FieldConfigPanel } from "./FieldConfigPanel"
import { DynamicField, DynamicProduct, ProductTemplate } from "@/lib/types"
import { createProductFromTemplate, generateSlugFromTitle } from "@/lib/utils/cms"
import { mockTemplates } from "@/lib/data/mockCMSData"
import { 
  Save, 
  Eye, 
  Smartphone, 
  Monitor, 
  Settings,
  Package,
  Layers,
  CheckCircle,
  AlertCircle,
  Star,
  Image as ImageIcon,
  Tag
} from "lucide-react"

interface ProductBuilderProps {
  product?: DynamicProduct
  onSave: (product: DynamicProduct) => void
  onCancel: () => void
}

export function ProductBuilder({ product, onSave, onCancel }: ProductBuilderProps) {
  const [currentProduct, setCurrentProduct] = useState<DynamicProduct | null>(null)
  const [selectedTemplate, setSelectedTemplate] = useState<ProductTemplate | null>(null)
  const [selectedField, setSelectedField] = useState<DynamicField | null>(null)
  const [isConfigPanelOpen, setIsConfigPanelOpen] = useState(false)
  const [previewMode, setPreviewMode] = useState<"desktop" | "mobile">("desktop")
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false)

  useEffect(() => {
    if (product) {
      setCurrentProduct(product)
      // Find the template this product was based on
      const template = mockTemplates.find(t => t.id === product.templateId)
      setSelectedTemplate(template || null)
    }
  }, [product])

  const handleTemplateSelect = (template: ProductTemplate) => {
    if (currentProduct && hasUnsavedChanges) {
      if (!confirm("سيتم فقدان التغييرات غير المحفوظة. هل تريد المتابعة؟")) {
        return
      }
    }
    
    const newProduct = createProductFromTemplate(template)
    setCurrentProduct(newProduct)
    setSelectedTemplate(template)
    setHasUnsavedChanges(true)
  }

  const handleProductChange = (updates: Partial<DynamicProduct>) => {
    if (!currentProduct) return
    
    let updatedProduct = { ...currentProduct, ...updates, updatedAt: new Date() }
    
    // Auto-generate slug from title if title changed
    if (updates.title && updates.title !== currentProduct.title) {
      updatedProduct.slug = generateSlugFromTitle(updates.title)
    }
    
    setCurrentProduct(updatedProduct)
    setHasUnsavedChanges(true)
  }

  const handleFieldsChange = (fields: DynamicField[]) => {
    handleProductChange({ fields })
  }

  const handleFieldSelect = (field: DynamicField) => {
    setSelectedField(field)
    setIsConfigPanelOpen(true)
  }

  const handleFieldUpdate = (updatedField: DynamicField) => {
    if (!currentProduct) return
    
    const updatedFields = currentProduct.fields.map(field =>
      field.id === updatedField.id ? updatedField : field
    )
    handleFieldsChange(updatedFields)
    setIsConfigPanelOpen(false)
    setSelectedField(null)
  }

  const handleSave = () => {
    if (!currentProduct) return
    onSave(currentProduct)
    setHasUnsavedChanges(false)
  }

  const isValid = currentProduct && 
    currentProduct.title.trim() !== "" && 
    currentProduct.slug.trim() !== "" &&
    currentProduct.category.trim() !== "" &&
    currentProduct.fields.length > 0

  if (!currentProduct) {
    return (
      <div className="space-y-6">
        {/* Template Selection */}
        <Card className="bg-slate-800/50 border-slate-700/50 backdrop-blur-sm">
          <CardHeader>
            <CardTitle className="text-white flex items-center gap-2">
              <Layers className="h-5 w-5" />
              اختر قالب المنتج
            </CardTitle>
            <p className="text-slate-400">
              ابدأ بقالب موجود أو أنشئ منتج من الصفر
            </p>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {mockTemplates.map((template) => (
                <Card 
                  key={template.id}
                  className="bg-slate-700/30 border-slate-600/50 hover:bg-slate-600/30 transition-all cursor-pointer group"
                  onClick={() => handleTemplateSelect(template)}
                >
                  <CardContent className="p-4">
                    <div className="flex items-start justify-between mb-3">
                      <div className="flex items-center gap-2">
                        <div className="p-2 bg-slate-600 rounded-lg">
                          <Layers className="h-4 w-4 text-slate-300" />
                        </div>
                        <div>
                          <h3 className="text-white font-medium">{template.name}</h3>
                          <p className="text-slate-400 text-sm">{template.description}</p>
                        </div>
                      </div>
                      {template.isActive && (
                        <Badge className="bg-green-500/20 text-green-400 text-xs">
                          نشط
                        </Badge>
                      )}
                    </div>
                    
                    <div className="flex items-center justify-between text-xs text-slate-400">
                      <span>{template.fields.length} حقل</span>
                      <span>{template.fields.filter(f => f.isRequired).length} مطلوب</span>
                    </div>
                  </CardContent>
                </Card>
              ))}
              
              {/* Create from scratch option */}
              <Card 
                className="bg-slate-700/30 border-slate-600/50 hover:bg-slate-600/30 transition-all cursor-pointer group border-dashed"
                onClick={() => {
                  const emptyProduct: DynamicProduct = {
                    id: `product_${Date.now()}`,
                    templateId: "",
                    slug: "",
                    title: "منتج جديد",
                    shortDescription: "",
                    image: "",
                    category: "",
                    inStock: true,
                    isActive: false,
                    fields: [],
                    metadata: {
                      featured: false,
                      popular: false,
                      tags: []
                    },
                    createdAt: new Date(),
                    updatedAt: new Date()
                  }
                  setCurrentProduct(emptyProduct)
                  setHasUnsavedChanges(true)
                }}
              >
                <CardContent className="p-4 text-center">
                  <div className="p-3 bg-slate-600 rounded-lg mx-auto w-fit mb-3">
                    <Package className="h-6 w-6 text-slate-300" />
                  </div>
                  <h3 className="text-white font-medium mb-2">إنشاء من الصفر</h3>
                  <p className="text-slate-400 text-sm">ابدأ بمنتج فارغ وأضف الحقول يدوياً</p>
                </CardContent>
              </Card>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Product Header */}
      <Card className="bg-slate-800/50 border-slate-700/50 backdrop-blur-sm">
        <CardHeader>
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-slate-700 rounded-lg">
                <Package className="h-5 w-5 text-slate-300" />
              </div>
              <div>
                <CardTitle className="text-white">
                  {product ? "تعديل المنتج" : "إنشاء منتج جديد"}
                </CardTitle>
                <p className="text-slate-400 text-sm">
                  {selectedTemplate ? `باستخدام قالب: ${selectedTemplate.name}` : "منتج مخصص"}
                </p>
              </div>
            </div>
            
            <div className="flex items-center gap-2">
              {hasUnsavedChanges && (
                <Badge className="bg-yellow-500/20 text-yellow-400">
                  تغييرات غير محفوظة
                </Badge>
              )}
              
              <div className="flex items-center gap-1 bg-slate-700 rounded-lg p-1">
                <Button
                  variant={previewMode === "desktop" ? "default" : "ghost"}
                  size="sm"
                  onClick={() => setPreviewMode("desktop")}
                  className="h-8 px-3"
                >
                  <Monitor className="h-4 w-4" />
                </Button>
                <Button
                  variant={previewMode === "mobile" ? "default" : "ghost"}
                  size="sm"
                  onClick={() => setPreviewMode("mobile")}
                  className="h-8 px-3"
                >
                  <Smartphone className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </div>
        </CardHeader>
        
        <CardContent className="space-y-4">
          {/* Basic Product Info */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label className="text-slate-300">اسم المنتج</Label>
              <Input
                value={currentProduct.title}
                onChange={(e) => handleProductChange({ title: e.target.value })}
                className="bg-slate-700 border-slate-600 text-white"
                placeholder="أدخل اسم المنتج"
              />
            </div>

            <div>
              <Label className="text-slate-300">الرابط (Slug)</Label>
              <Input
                value={currentProduct.slug}
                onChange={(e) => handleProductChange({ slug: e.target.value })}
                className="bg-slate-700 border-slate-600 text-white"
                placeholder="product-slug"
              />
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label className="text-slate-300">الفئة</Label>
              <Input
                value={currentProduct.category}
                onChange={(e) => handleProductChange({ category: e.target.value })}
                className="bg-slate-700 border-slate-600 text-white"
                placeholder="فئة المنتج"
              />
            </div>

            <div>
              <Label className="text-slate-300">رابط الصورة</Label>
              <Input
                value={currentProduct.image}
                onChange={(e) => handleProductChange({ image: e.target.value })}
                className="bg-slate-700 border-slate-600 text-white"
                placeholder="https://example.com/image.jpg"
              />
            </div>
          </div>

          <div>
            <Label className="text-slate-300">الوصف القصير</Label>
            <Input
              value={currentProduct.shortDescription}
              onChange={(e) => handleProductChange({ shortDescription: e.target.value })}
              className="bg-slate-700 border-slate-600 text-white"
              placeholder="وصف مختصر للمنتج"
            />
          </div>

          {/* Product Settings */}
          <div className="flex flex-wrap gap-4 pt-2">
            <div className="flex items-center gap-2">
              <Button
                variant={currentProduct.isActive ? "default" : "ghost"}
                size="sm"
                onClick={() => handleProductChange({ isActive: !currentProduct.isActive })}
                className={currentProduct.isActive ? "bg-green-500 hover:bg-green-600" : ""}
              >
                <Eye className="h-4 w-4 mr-1" />
                {currentProduct.isActive ? "نشط" : "غير نشط"}
              </Button>
            </div>

            <div className="flex items-center gap-2">
              <Button
                variant={currentProduct.inStock ? "default" : "ghost"}
                size="sm"
                onClick={() => handleProductChange({ inStock: !currentProduct.inStock })}
                className={currentProduct.inStock ? "bg-blue-500 hover:bg-blue-600" : ""}
              >
                <Package className="h-4 w-4 mr-1" />
                {currentProduct.inStock ? "متوفر" : "غير متوفر"}
              </Button>
            </div>

            <div className="flex items-center gap-2">
              <Button
                variant={currentProduct.metadata.featured ? "default" : "ghost"}
                size="sm"
                onClick={() => handleProductChange({ 
                  metadata: { 
                    ...currentProduct.metadata, 
                    featured: !currentProduct.metadata.featured 
                  }
                })}
                className={currentProduct.metadata.featured ? "bg-yellow-500 hover:bg-yellow-600 text-slate-900" : ""}
              >
                <Star className="h-4 w-4 mr-1" />
                مميز
              </Button>
            </div>

            <div className="flex items-center gap-2">
              <Button
                variant={currentProduct.metadata.popular ? "default" : "ghost"}
                size="sm"
                onClick={() => handleProductChange({ 
                  metadata: { 
                    ...currentProduct.metadata, 
                    popular: !currentProduct.metadata.popular 
                  }
                })}
                className={currentProduct.metadata.popular ? "bg-orange-500 hover:bg-orange-600" : ""}
              >
                <Tag className="h-4 w-4 mr-1" />
                الأكثر طلباً
              </Button>
            </div>
          </div>

          {/* Product Stats */}
          <div className="flex items-center gap-4 pt-2 border-t border-slate-700">
            <div className="flex items-center gap-2">
              <Layers className="h-4 w-4 text-slate-400" />
              <span className="text-slate-400 text-sm">
                {currentProduct.fields.length} حقل
              </span>
            </div>
            
            <div className="flex items-center gap-2">
              <Settings className="h-4 w-4 text-slate-400" />
              <span className="text-slate-400 text-sm">
                {currentProduct.fields.filter(f => f.isRequired).length} مطلوب
              </span>
            </div>

            <div className="flex items-center gap-2">
              <Eye className="h-4 w-4 text-slate-400" />
              <span className="text-slate-400 text-sm">
                {currentProduct.fields.filter(f => f.isVisible).length} مرئي
              </span>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Main Content Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Field Builder */}
        <div className="space-y-6">
          <FieldBuilder
            fields={currentProduct.fields}
            onFieldsChange={handleFieldsChange}
            onFieldSelect={handleFieldSelect}
            selectedFieldId={selectedField?.id}
          />
        </div>

        {/* Preview Panel */}
        <div className="space-y-6">
          <Card className="bg-slate-800/50 border-slate-700/50 backdrop-blur-sm">
            <CardHeader>
              <CardTitle className="text-white flex items-center gap-2">
                <Eye className="h-5 w-5" />
                معاينة المنتج
                <Badge variant="secondary" className="bg-slate-700 text-slate-300">
                  {previewMode === "desktop" ? "ديسكتوب" : "موبايل"}
                </Badge>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className={`
                bg-slate-900 rounded-lg p-4 border border-slate-600
                ${previewMode === "mobile" ? "max-w-sm mx-auto" : ""}
              `}>
                {/* Product Header Preview */}
                <div className="mb-6">
                  <div className="w-full h-32 bg-slate-700 rounded-lg mb-4 flex items-center justify-center">
                    {currentProduct.image ? (
                      <img 
                        src={currentProduct.image} 
                        alt={currentProduct.title}
                        className="w-full h-full object-cover rounded-lg"
                        onError={(e) => {
                          e.currentTarget.style.display = 'none'
                          e.currentTarget.nextElementSibling?.classList.remove('hidden')
                        }}
                      />
                    ) : (
                      <ImageIcon className="h-8 w-8 text-slate-400" />
                    )}
                    <div className="hidden flex items-center justify-center w-full h-full">
                      <span className="text-slate-400">صورة المنتج</span>
                    </div>
                  </div>
                  <h3 className="text-white font-bold text-lg mb-2">{currentProduct.title}</h3>
                  <p className="text-slate-400 text-sm">{currentProduct.shortDescription}</p>
                  
                  <div className="flex items-center gap-2 mt-2">
                    <Badge className="bg-slate-700 text-slate-300 text-xs">
                      {currentProduct.category}
                    </Badge>
                    {currentProduct.metadata.featured && (
                      <Badge className="bg-yellow-500/20 text-yellow-400 text-xs">
                        مميز
                      </Badge>
                    )}
                    {currentProduct.metadata.popular && (
                      <Badge className="bg-orange-500/20 text-orange-400 text-xs">
                        الأكثر طلباً
                      </Badge>
                    )}
                  </div>
                </div>

                {/* Fields Preview */}
                <div className="space-y-4">
                  {currentProduct.fields
                    .filter(field => field.isVisible)
                    .sort((a, b) => a.order - b.order)
                    .map((field) => (
                      <div key={field.id} className="space-y-2">
                        <Label className="text-slate-300 flex items-center gap-1">
                          {field.label}
                          {field.isRequired && (
                            <span className="text-red-400">*</span>
                          )}
                        </Label>
                        
                        {field.type === "text" && (
                          <Input
                            placeholder={field.placeholder}
                            className="bg-slate-700 border-slate-600 text-white"
                            disabled
                          />
                        )}
                        
                        {field.type === "email" && (
                          <Input
                            type="email"
                            placeholder={field.placeholder}
                            className="bg-slate-700 border-slate-600 text-white"
                            disabled
                          />
                        )}
                        
                        {field.type === "number" && (
                          <Input
                            type="number"
                            placeholder={field.placeholder}
                            className="bg-slate-700 border-slate-600 text-white"
                            disabled
                          />
                        )}
                        
                        {field.type === "textarea" && (
                          <textarea
                            placeholder={field.placeholder}
                            className="w-full p-2 bg-slate-700 border border-slate-600 rounded text-white text-sm h-20 resize-none"
                            disabled
                          />
                        )}
                        
                        {field.type === "select" && (
                          <select className="w-full p-2 bg-slate-700 border border-slate-600 rounded text-white" disabled>
                            <option>{field.placeholder || "اختر..."}</option>
                            {field.options?.map(option => (
                              <option key={option.id}>{option.label}</option>
                            ))}
                          </select>
                        )}
                        
                        {field.type === "package_selection" && (
                          <div className={`grid gap-2 ${previewMode === "mobile" ? "grid-cols-1" : "grid-cols-2"}`}>
                            {field.options?.map(option => (
                              <div 
                                key={option.id} 
                                className={`p-3 bg-slate-700 border border-slate-600 rounded-lg text-center cursor-pointer hover:border-yellow-400 transition-colors ${option.popular ? "border-yellow-400" : ""}`}
                              >
                                <p className="text-white font-medium">{option.label}</p>
                                {option.price && (
                                  <div className="flex items-center justify-center gap-2 mt-1">
                                    <span className="text-yellow-400 font-bold">{option.price} ج.س</span>
                                    {option.originalPrice && option.originalPrice > option.price && (
                                      <span className="text-slate-400 line-through text-sm">{option.originalPrice}</span>
                                    )}
                                  </div>
                                )}
                                {option.popular && (
                                  <Badge className="bg-yellow-400 text-slate-900 text-xs mt-1">
                                    الأكثر طلباً
                                  </Badge>
                                )}
                              </div>
                            ))}
                          </div>
                        )}

                        {field.description && (
                          <p className="text-slate-400 text-xs">{field.description}</p>
                        )}
                      </div>
                    ))}

                  {currentProduct.fields.filter(f => f.isVisible).length === 0 && (
                    <div className="text-center py-8 text-slate-400">
                      <p>لا توجد حقول مرئية</p>
                      <p className="text-sm">أضف حقول من الجانب الأيسر لرؤية المعاينة</p>
                    </div>
                  )}
                </div>

                {/* Action Button Preview */}
                {currentProduct.fields.length > 0 && (
                  <div className="mt-6 pt-4 border-t border-slate-600">
                    <Button className="w-full bg-gradient-to-r from-yellow-400 to-orange-500 hover:from-yellow-500 hover:to-orange-600 text-slate-900 font-bold" disabled>
                      إضافة إلى السلة
                    </Button>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Action Buttons */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          {isValid ? (
            <div className="flex items-center gap-2 text-green-400">
              <CheckCircle className="h-4 w-4" />
              <span className="text-sm">المنتج جاهز للحفظ</span>
            </div>
          ) : (
            <div className="flex items-center gap-2 text-red-400">
              <AlertCircle className="h-4 w-4" />
              <span className="text-sm">يرجى إكمال المعلومات المطلوبة</span>
            </div>
          )}
        </div>

        <div className="flex items-center gap-3">
          <Button variant="ghost" onClick={onCancel}>
            إلغاء
          </Button>
          <Button 
            onClick={handleSave}
            disabled={!isValid}
            className="bg-green-500 hover:bg-green-600"
          >
            <Save className="h-4 w-4 mr-2" />
            {product ? "حفظ التغييرات" : "إنشاء المنتج"}
          </Button>
        </div>
      </div>

      {/* Field Configuration Panel */}
      <FieldConfigPanel
        field={selectedField}
        onUpdate={handleFieldUpdate}
        onClose={() => {
          setIsConfigPanelOpen(false)
          setSelectedField(null)
        }}
        isOpen={isConfigPanelOpen}
      />
    </div>
  )
}
