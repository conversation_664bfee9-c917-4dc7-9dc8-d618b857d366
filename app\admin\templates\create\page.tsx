"use client"

import { useRouter } from "next/navigation"
import { AdminLayout } from "@/components/admin/AdminLayout"
import { TemplateBuilder } from "@/components/admin/TemplateBuilder"
import { ProductTemplate } from "@/lib/types"
import { ArrowLeft } from "lucide-react"
import { Button } from "@/components/ui/button"

export default function CreateTemplatePage() {
  const router = useRouter()

  const handleSave = (template: ProductTemplate) => {
    // TODO: Save to Supabase
    console.log("Saving template:", template)
    
    // For now, just show success and redirect
    alert("تم إنشاء القالب بنجاح!")
    router.push("/admin/templates")
  }

  const handleCancel = () => {
    router.push("/admin/templates")
  }

  return (
    <AdminLayout 
      title="إنشاء قالب جديد" 
      subtitle="أنشئ قالب منتج قابل للإعادة الاستخدام"
      actions={
        <Button 
          variant="ghost"
          onClick={() => router.push("/admin/templates")}
          className="text-slate-400 hover:text-white"
        >
          <ArrowLeft className="h-4 w-4 ml-2" />
          العودة للقوالب
        </Button>
      }
    >
      <TemplateBuilder
        onSave={handleSave}
        onCancel={handleCancel}
      />
    </AdminLayout>
  )
}
