"use client"

import { useState } from "react"
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Badge } from "@/components/ui/badge"
import { FieldBuilder } from "./FieldBuilder"
import { FieldConfigPanel } from "./FieldConfigPanel"
import { DynamicField, ProductTemplate } from "@/lib/types"
import { createEmptyTemplate } from "@/lib/utils/cms"
import { 
  Save, 
  Eye, 
  Smartphone, 
  Monitor, 
  Settings,
  FileText,
  Layers,
  CheckCircle
} from "lucide-react"

interface TemplateBuilderProps {
  template?: ProductTemplate
  onSave: (template: ProductTemplate) => void
  onCancel: () => void
}

export function TemplateBuilder({ template, onSave, onCancel }: TemplateBuilderProps) {
  const [currentTemplate, setCurrentTemplate] = useState<ProductTemplate>(
    template || createEmptyTemplate()
  )
  const [selectedField, setSelectedField] = useState<DynamicField | null>(null)
  const [isConfigPanelOpen, setIsConfigPanelOpen] = useState(false)
  const [previewMode, setPreviewMode] = useState<"desktop" | "mobile">("desktop")
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false)

  const handleTemplateChange = (updates: Partial<ProductTemplate>) => {
    setCurrentTemplate(prev => ({ ...prev, ...updates, updatedAt: new Date() }))
    setHasUnsavedChanges(true)
  }

  const handleFieldsChange = (fields: DynamicField[]) => {
    handleTemplateChange({ fields })
  }

  const handleFieldSelect = (field: DynamicField) => {
    setSelectedField(field)
    setIsConfigPanelOpen(true)
  }

  const handleFieldUpdate = (updatedField: DynamicField) => {
    const updatedFields = currentTemplate.fields.map(field =>
      field.id === updatedField.id ? updatedField : field
    )
    handleFieldsChange(updatedFields)
    setIsConfigPanelOpen(false)
    setSelectedField(null)
  }

  const handleSave = () => {
    onSave(currentTemplate)
    setHasUnsavedChanges(false)
  }

  const isValid = currentTemplate.name.trim() !== "" && currentTemplate.fields.length > 0

  return (
    <div className="space-y-6">
      {/* Template Header */}
      <Card className="bg-slate-800/50 border-slate-700/50 backdrop-blur-sm">
        <CardHeader>
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-slate-700 rounded-lg">
                <FileText className="h-5 w-5 text-slate-300" />
              </div>
              <div>
                <CardTitle className="text-white">
                  {template ? "تعديل القالب" : "إنشاء قالب جديد"}
                </CardTitle>
                <p className="text-slate-400 text-sm">
                  قم بإنشاء قالب قابل للإعادة الاستخدام لتسريع إنشاء المنتجات
                </p>
              </div>
            </div>
            
            <div className="flex items-center gap-2">
              {hasUnsavedChanges && (
                <Badge className="bg-yellow-500/20 text-yellow-400">
                  تغييرات غير محفوظة
                </Badge>
              )}
              
              <div className="flex items-center gap-1 bg-slate-700 rounded-lg p-1">
                <Button
                  variant={previewMode === "desktop" ? "default" : "ghost"}
                  size="sm"
                  onClick={() => setPreviewMode("desktop")}
                  className="h-8 px-3"
                >
                  <Monitor className="h-4 w-4" />
                </Button>
                <Button
                  variant={previewMode === "mobile" ? "default" : "ghost"}
                  size="sm"
                  onClick={() => setPreviewMode("mobile")}
                  className="h-8 px-3"
                >
                  <Smartphone className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </div>
        </CardHeader>
        
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label className="text-slate-300">اسم القالب</Label>
              <Input
                value={currentTemplate.name}
                onChange={(e) => handleTemplateChange({ name: e.target.value })}
                className="bg-slate-700 border-slate-600 text-white"
                placeholder="أدخل اسم القالب"
              />
            </div>

            <div>
              <Label className="text-slate-300">الفئة</Label>
              <Input
                value={currentTemplate.category || ""}
                onChange={(e) => handleTemplateChange({ category: e.target.value })}
                className="bg-slate-700 border-slate-600 text-white"
                placeholder="فئة القالب (اختياري)"
              />
            </div>
          </div>

          <div>
            <Label className="text-slate-300">الوصف</Label>
            <Input
              value={currentTemplate.description}
              onChange={(e) => handleTemplateChange({ description: e.target.value })}
              className="bg-slate-700 border-slate-600 text-white"
              placeholder="وصف مختصر للقالب"
            />
          </div>

          {/* Template Stats */}
          <div className="flex items-center gap-4 pt-2">
            <div className="flex items-center gap-2">
              <Layers className="h-4 w-4 text-slate-400" />
              <span className="text-slate-400 text-sm">
                {currentTemplate.fields.length} حقل
              </span>
            </div>
            
            <div className="flex items-center gap-2">
              <Settings className="h-4 w-4 text-slate-400" />
              <span className="text-slate-400 text-sm">
                {currentTemplate.fields.filter(f => f.isRequired).length} مطلوب
              </span>
            </div>

            <div className="flex items-center gap-2">
              <Eye className="h-4 w-4 text-slate-400" />
              <span className="text-slate-400 text-sm">
                {currentTemplate.fields.filter(f => f.isVisible).length} مرئي
              </span>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Main Content Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Field Builder */}
        <div className="space-y-6">
          <FieldBuilder
            fields={currentTemplate.fields}
            onFieldsChange={handleFieldsChange}
            onFieldSelect={handleFieldSelect}
            selectedFieldId={selectedField?.id}
          />
        </div>

        {/* Preview Panel */}
        <div className="space-y-6">
          <Card className="bg-slate-800/50 border-slate-700/50 backdrop-blur-sm">
            <CardHeader>
              <CardTitle className="text-white flex items-center gap-2">
                <Eye className="h-5 w-5" />
                معاينة المنتج
                <Badge variant="secondary" className="bg-slate-700 text-slate-300">
                  {previewMode === "desktop" ? "ديسكتوب" : "موبايل"}
                </Badge>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className={`
                bg-slate-900 rounded-lg p-4 border border-slate-600
                ${previewMode === "mobile" ? "max-w-sm mx-auto" : ""}
              `}>
                {/* Product Header Preview */}
                <div className="mb-6">
                  <div className="w-full h-32 bg-slate-700 rounded-lg mb-4 flex items-center justify-center">
                    <span className="text-slate-400">صورة المنتج</span>
                  </div>
                  <h3 className="text-white font-bold text-lg mb-2">اسم المنتج</h3>
                  <p className="text-slate-400 text-sm">وصف قصير للمنتج</p>
                </div>

                {/* Fields Preview */}
                <div className="space-y-4">
                  {currentTemplate.fields
                    .filter(field => field.isVisible)
                    .sort((a, b) => a.order - b.order)
                    .map((field) => (
                      <div key={field.id} className="space-y-2">
                        <Label className="text-slate-300 flex items-center gap-1">
                          {field.label}
                          {field.isRequired && (
                            <span className="text-red-400">*</span>
                          )}
                        </Label>
                        
                        {field.type === "text" && (
                          <Input
                            placeholder={field.placeholder}
                            className="bg-slate-700 border-slate-600 text-white"
                            disabled
                          />
                        )}
                        
                        {field.type === "email" && (
                          <Input
                            type="email"
                            placeholder={field.placeholder}
                            className="bg-slate-700 border-slate-600 text-white"
                            disabled
                          />
                        )}
                        
                        {field.type === "number" && (
                          <Input
                            type="number"
                            placeholder={field.placeholder}
                            className="bg-slate-700 border-slate-600 text-white"
                            disabled
                          />
                        )}
                        
                        {field.type === "textarea" && (
                          <textarea
                            placeholder={field.placeholder}
                            className="w-full p-2 bg-slate-700 border border-slate-600 rounded text-white text-sm h-20 resize-none"
                            disabled
                          />
                        )}
                        
                        {field.type === "select" && (
                          <select className="w-full p-2 bg-slate-700 border border-slate-600 rounded text-white" disabled>
                            <option>{field.placeholder || "اختر..."}</option>
                            {field.options?.map(option => (
                              <option key={option.id}>{option.label}</option>
                            ))}
                          </select>
                        )}
                        
                        {field.type === "package_selection" && (
                          <div className={`grid gap-2 ${previewMode === "mobile" ? "grid-cols-1" : "grid-cols-2"}`}>
                            {field.options?.map(option => (
                              <div 
                                key={option.id} 
                                className={`p-3 bg-slate-700 border border-slate-600 rounded-lg text-center cursor-pointer hover:border-yellow-400 transition-colors ${option.popular ? "border-yellow-400" : ""}`}
                              >
                                <p className="text-white font-medium">{option.label}</p>
                                {option.price && (
                                  <div className="flex items-center justify-center gap-2 mt-1">
                                    <span className="text-yellow-400 font-bold">{option.price} ج.س</span>
                                    {option.originalPrice && option.originalPrice > option.price && (
                                      <span className="text-slate-400 line-through text-sm">{option.originalPrice}</span>
                                    )}
                                  </div>
                                )}
                                {option.popular && (
                                  <Badge className="bg-yellow-400 text-slate-900 text-xs mt-1">
                                    الأكثر طلباً
                                  </Badge>
                                )}
                              </div>
                            ))}
                          </div>
                        )}

                        {field.description && (
                          <p className="text-slate-400 text-xs">{field.description}</p>
                        )}
                      </div>
                    ))}

                  {currentTemplate.fields.filter(f => f.isVisible).length === 0 && (
                    <div className="text-center py-8 text-slate-400">
                      <p>لا توجد حقول مرئية</p>
                      <p className="text-sm">أضف حقول من الجانب الأيسر لرؤية المعاينة</p>
                    </div>
                  )}
                </div>

                {/* Action Button Preview */}
                {currentTemplate.fields.length > 0 && (
                  <div className="mt-6 pt-4 border-t border-slate-600">
                    <Button className="w-full bg-gradient-to-r from-yellow-400 to-orange-500 hover:from-yellow-500 hover:to-orange-600 text-slate-900 font-bold" disabled>
                      إضافة إلى السلة
                    </Button>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Action Buttons */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          {isValid && (
            <div className="flex items-center gap-2 text-green-400">
              <CheckCircle className="h-4 w-4" />
              <span className="text-sm">القالب جاهز للحفظ</span>
            </div>
          )}
        </div>

        <div className="flex items-center gap-3">
          <Button variant="ghost" onClick={onCancel}>
            إلغاء
          </Button>
          <Button 
            onClick={handleSave}
            disabled={!isValid}
            className="bg-green-500 hover:bg-green-600"
          >
            <Save className="h-4 w-4 mr-2" />
            {template ? "حفظ التغييرات" : "إنشاء القالب"}
          </Button>
        </div>
      </div>

      {/* Field Configuration Panel */}
      <FieldConfigPanel
        field={selectedField}
        onUpdate={handleFieldUpdate}
        onClose={() => {
          setIsConfigPanelOpen(false)
          setSelectedField(null)
        }}
        isOpen={isConfigPanelOpen}
      />
    </div>
  )
}
