"use client"

import { useState, useCallback } from "react"
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON>eader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Ta<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ist, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { 
  Plus, 
  Search, 
  Filter, 
  Edit, 
  Trash2, 
  Eye, 
  Copy,
  Package,
  Grid,
  List,
  MoreVertical
} from "lucide-react"
import { ProductTemplate } from "@/lib/types"
import { ProductForm } from "./ProductForm"

// Mock data for templates - will be replaced with Supabase
const mockTemplates: ProductTemplate[] = [
  {
    id: "template_1",
    name: "قالب Free Fire الجواهر",
    description: "قالب مخصص لمنتجات Free Fire مع محدد الحزم ومعرف اللاعب",
    category: "gaming",
    fields: [
      {
        id: "field_1",
        type: "heading",
        label: "شحن جواهر Free Fire",
        name: "product_title",
        required: false,
        order: 0,
        visible: true,
        level: 1,
        alignment: "center"
      },
      {
        id: "field_2",
        type: "package_selector",
        label: "اختر حزمة الجواهر",
        name: "diamond_package",
        required: true,
        order: 1,
        visible: true,
        description: "اختر الحزمة المناسبة لك",
        packages: [
          {
            id: "pkg_1",
            name: "100 جوهرة",
            amount: "100 جوهرة",
            price: 35,
            originalPrice: 40,
            discount: 12,
            description: "حزمة صغيرة للمبتدئين"
          },
          {
            id: "pkg_2",
            name: "310 جوهرة",
            amount: "310 جوهرة",
            price: 100,
            originalPrice: 120,
            discount: 17,
            popular: true,
            description: "الحزمة الأكثر شعبية"
          },
          {
            id: "pkg_3",
            name: "520 جوهرة",
            amount: "520 جوهرة",
            price: 160,
            originalPrice: 200,
            discount: 20,
            description: "حزمة كبيرة للاعبين المحترفين"
          }
        ],
        displayStyle: "cards"
      },
      {
        id: "field_3",
        type: "text",
        label: "معرف اللاعب",
        name: "player_id",
        required: true,
        order: 2,
        visible: true,
        placeholder: "أدخل معرف اللاعب الخاص بك",
        description: "يمكنك العثور على معرف اللاعب في إعدادات اللعبة",
        validation: {
          min: 6,
          max: 12,
          pattern: "^[0-9]+$",
          customMessage: "معرف اللاعب يجب أن يكون أرقام فقط"
        }
      },
      {
        id: "field_4",
        type: "quantity_selector",
        label: "الكمية",
        name: "quantity",
        required: true,
        order: 3,
        visible: true,
        min: 1,
        max: 10,
        defaultValue: 1
      }
    ],
    layout: {
      sections: [],
      theme: {
        primaryColor: "#f59e0b",
        secondaryColor: "#3b82f6",
        backgroundColor: "#0f172a",
        textColor: "#ffffff",
        borderRadius: "medium",
        shadows: true,
        animations: true
      }
    },
    createdAt: new Date("2024-01-15"),
    updatedAt: new Date("2024-01-20"),
    isDefault: true
  },
  {
    id: "template_2",
    name: "قالب PUBG Mobile UC",
    description: "قالب لشحن عملة UC في لعبة PUBG Mobile",
    category: "gaming",
    fields: [
      {
        id: "field_1",
        type: "heading",
        label: "شحن يوسي PUBG Mobile",
        name: "product_title",
        required: false,
        order: 0,
        visible: true,
        level: 1,
        alignment: "center"
      },
      {
        id: "field_2",
        type: "select",
        label: "اختر السيرفر",
        name: "server",
        required: true,
        order: 1,
        visible: true,
        description: "اختر السيرفر الخاص بك",
        options: [
          { id: "opt_1", label: "الشرق الأوسط", value: "middle_east", price: 0 },
          { id: "opt_2", label: "أوروبا", value: "europe", price: 0 },
          { id: "opt_3", label: "آسيا", value: "asia", price: 0 },
          { id: "opt_4", label: "أمريكا الشمالية", value: "north_america", price: 5 }
        ]
      },
      {
        id: "field_3",
        type: "package_selector",
        label: "اختر حزمة UC",
        name: "uc_package",
        required: true,
        order: 2,
        visible: true,
        packages: [
          {
            id: "pkg_1",
            name: "60 UC",
            amount: "60 يوسي",
            price: 25,
            originalPrice: 30,
            discount: 17
          },
          {
            id: "pkg_2",
            name: "325 UC",
            amount: "325 يوسي",
            price: 120,
            originalPrice: 150,
            discount: 20,
            popular: true
          },
          {
            id: "pkg_3",
            name: "660 UC",
            amount: "660 يوسي",
            price: 240,
            originalPrice: 300,
            discount: 20
          }
        ],
        displayStyle: "grid"
      },
      {
        id: "field_4",
        type: "number",
        label: "معرف اللاعب",
        name: "player_id",
        required: true,
        order: 3,
        visible: true,
        placeholder: "123456789",
        validation: {
          min: 100000000,
          max: 9999999999,
          customMessage: "معرف اللاعب يجب أن يكون بين 9-10 أرقام"
        }
      }
    ],
    layout: {
      sections: [],
      theme: {
        primaryColor: "#10b981",
        secondaryColor: "#8b5cf6",
        backgroundColor: "#0f172a",
        textColor: "#ffffff",
        borderRadius: "medium",
        shadows: true,
        animations: true
      }
    },
    createdAt: new Date("2024-01-10"),
    updatedAt: new Date("2024-01-18"),
    isDefault: false
  },
  {
    id: "template_3",
    name: "قالب TikTok Coins",
    description: "قالب لشحن عملات TikTok مع حقول متقدمة",
    category: "digital",
    fields: [
      {
        id: "field_1",
        type: "heading",
        label: "شحن عملات تيك توك",
        name: "product_title",
        required: false,
        order: 0,
        visible: true,
        level: 1,
        alignment: "center"
      },
      {
        id: "field_2",
        type: "package_selector",
        label: "اختر حزمة العملات",
        name: "coins_package",
        required: true,
        order: 1,
        visible: true,
        packages: [
          {
            id: "pkg_1",
            name: "100 عملة",
            amount: "100 عملة",
            price: 50,
            description: "حزمة صغيرة"
          },
          {
            id: "pkg_2",
            name: "500 عملة",
            amount: "500 عملة",
            price: 200,
            originalPrice: 250,
            discount: 20,
            popular: true
          },
          {
            id: "pkg_3",
            name: "1000 عملة",
            amount: "1000 عملة",
            price: 350,
            originalPrice: 450,
            discount: 22
          }
        ],
        displayStyle: "cards"
      },
      {
        id: "field_3",
        type: "text",
        label: "اسم المستخدم في TikTok",
        name: "username",
        required: true,
        order: 2,
        visible: true,
        placeholder: "@username",
        description: "أدخل اسم المستخدم بدون @"
      },
      {
        id: "field_4",
        type: "email",
        label: "البريد الإلكتروني للتأكيد",
        name: "email",
        required: true,
        order: 3,
        visible: true,
        placeholder: "<EMAIL>",
        description: "سنرسل تأكيد الطلب على هذا البريد"
      },
      {
        id: "field_5",
        type: "checkbox",
        label: "أوافق على الشروط والأحكام",
        name: "terms_agreement",
        required: true,
        order: 4,
        visible: true
      }
    ],
    layout: {
      sections: [],
      theme: {
        primaryColor: "#ec4899",
        secondaryColor: "#06b6d4",
        backgroundColor: "#0f172a",
        textColor: "#ffffff",
        borderRadius: "medium",
        shadows: true,
        animations: true
      }
    },
    createdAt: new Date("2024-01-12"),
    updatedAt: new Date("2024-01-19"),
    isDefault: false
  }
]

export function ProductDashboard() {
  const [templates, setTemplates] = useState<ProductTemplate[]>(mockTemplates)
  const [searchTerm, setSearchTerm] = useState("")
  const [selectedCategory, setSelectedCategory] = useState<string>("all")
  const [viewMode, setViewMode] = useState<"grid" | "list">("grid")
  const [editingTemplate, setEditingTemplate] = useState<ProductTemplate | null>(null)
  const [showCreateForm, setShowCreateForm] = useState(false)
  const [showEditForm, setShowEditForm] = useState(false)

  // Filter templates
  const filteredTemplates = templates.filter(template => {
    const matchesSearch = template.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         template.description?.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesCategory = selectedCategory === "all" || template.category === selectedCategory
    
    return matchesSearch && matchesCategory
  })

  // Handle template operations
  const handleCreateTemplate = useCallback(() => {
    setEditingTemplate(null)
    setShowCreateForm(true)
  }, [])

  const handleEditTemplate = useCallback((template: ProductTemplate) => {
    setEditingTemplate(template)
    setShowEditForm(true)
  }, [])

  const handleSaveTemplate = useCallback((template: ProductTemplate) => {
    if (editingTemplate) {
      // Update existing template
      setTemplates(prev => prev.map(t => t.id === template.id ? template : t))
    } else {
      // Create new template
      setTemplates(prev => [...prev, template])
    }
    setShowCreateForm(false)
    setShowEditForm(false)
    setEditingTemplate(null)
  }, [editingTemplate])

  const handleCloseForm = useCallback(() => {
    setShowCreateForm(false)
    setShowEditForm(false)
    setEditingTemplate(null)
  }, [])

  const handlePreviewTemplate = useCallback((template: ProductTemplate) => {
    // Open preview in new tab or modal
    console.log("Preview template:", template)
  }, [])

  const handleDeleteTemplate = useCallback((templateId: string) => {
    if (confirm("هل أنت متأكد من حذف هذا القالب؟")) {
      setTemplates(prev => prev.filter(t => t.id !== templateId))
    }
  }, [])

  const handleDuplicateTemplate = useCallback((template: ProductTemplate) => {
    const duplicatedTemplate: ProductTemplate = {
      ...template,
      id: `template_${Date.now()}`,
      name: `${template.name} - نسخة`,
      createdAt: new Date(),
      updatedAt: new Date(),
      isDefault: false
    }
    setTemplates(prev => [...prev, duplicatedTemplate])
  }, [])



  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900 text-white">
      <div className="container mx-auto p-4 lg:p-6 max-w-7xl">
        {/* Header */}
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4 mb-6">
          <div>
            <h1 className="text-2xl lg:text-3xl font-bold bg-gradient-to-r from-yellow-400 to-orange-500 bg-clip-text text-transparent">
              إدارة المنتجات
            </h1>
            <p className="text-slate-400 mt-1">
              إنشاء وإدارة قوالب المنتجات القابلة للتخصيص
            </p>
          </div>
          
          <Button
            onClick={handleCreateTemplate}
            className="bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 w-full lg:w-auto"
          >
            <Plus className="h-4 w-4 ml-2" />
            إنشاء قالب جديد
          </Button>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
          <Card className="bg-slate-800/50 border-slate-700/50 backdrop-blur-sm">
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-slate-400 text-sm">إجمالي القوالب</p>
                  <p className="text-2xl font-bold text-white">{templates.length}</p>
                </div>
                <Package className="h-8 w-8 text-blue-400" />
              </div>
            </CardContent>
          </Card>
          
          <Card className="bg-slate-800/50 border-slate-700/50 backdrop-blur-sm">
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-slate-400 text-sm">القوالب النشطة</p>
                  <p className="text-2xl font-bold text-white">{templates.filter(t => !t.isDefault).length}</p>
                </div>
                <Grid className="h-8 w-8 text-green-400" />
              </div>
            </CardContent>
          </Card>
          
          <Card className="bg-slate-800/50 border-slate-700/50 backdrop-blur-sm">
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-slate-400 text-sm">الفئات</p>
                  <p className="text-2xl font-bold text-white">{new Set(templates.map(t => t.category)).size}</p>
                </div>
                <Filter className="h-8 w-8 text-purple-400" />
              </div>
            </CardContent>
          </Card>
          
          <Card className="bg-slate-800/50 border-slate-700/50 backdrop-blur-sm">
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-slate-400 text-sm">آخر تحديث</p>
                  <p className="text-sm font-medium text-white">اليوم</p>
                </div>
                <Eye className="h-8 w-8 text-yellow-400" />
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Filters and Search */}
        <Card className="bg-slate-800/50 border-slate-700/50 backdrop-blur-sm mb-6">
          <CardContent className="p-4">
            <div className="flex flex-col gap-4">
              {/* Search */}
              <div className="relative">
                <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-slate-400 h-4 w-4" />
                <Input
                  placeholder="ابحث عن القوالب..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pr-10 bg-slate-700/50 border-slate-600 text-white"
                />
              </div>

              <div className="flex flex-col sm:flex-row gap-4">
                {/* Category Filter */}
                <div className="flex gap-2 overflow-x-auto pb-2 sm:pb-0">
                  {["all", "gaming", "digital", "subscriptions", "gift-cards"].map((category) => (
                    <Button
                      key={category}
                      variant={selectedCategory === category ? "default" : "outline"}
                      size="sm"
                      onClick={() => setSelectedCategory(category)}
                      className="whitespace-nowrap border-slate-600 text-xs sm:text-sm"
                    >
                      {category === "all" ? "الكل" :
                       category === "gaming" ? "ألعاب" :
                       category === "digital" ? "رقمية" :
                       category === "subscriptions" ? "اشتراكات" :
                       "بطاقات هدايا"}
                    </Button>
                  ))}
                </div>

                {/* View Mode */}
                <div className="flex gap-1 justify-center sm:justify-start">
                  <Button
                    variant={viewMode === "grid" ? "default" : "outline"}
                    size="sm"
                    onClick={() => setViewMode("grid")}
                    className="border-slate-600"
                  >
                    <Grid className="h-4 w-4" />
                  </Button>
                  <Button
                    variant={viewMode === "list" ? "default" : "outline"}
                    size="sm"
                    onClick={() => setViewMode("list")}
                    className="border-slate-600"
                  >
                    <List className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Templates Grid/List */}
        {filteredTemplates.length === 0 ? (
          <Card className="bg-slate-800/50 border-slate-700/50 backdrop-blur-sm">
            <CardContent className="p-8 text-center">
              <Package className="h-16 w-16 text-slate-400 mx-auto mb-4" />
              <h3 className="text-xl font-bold text-white mb-2">لا توجد قوالب</h3>
              <p className="text-slate-300 mb-4">
                {searchTerm ? "لم يتم العثور على قوالب تطابق البحث" : "ابدأ بإنشاء أول قالب منتج"}
              </p>
              {!searchTerm && (
                <Button
                  onClick={handleCreateTemplate}
                  className="bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700"
                >
                  <Plus className="h-4 w-4 ml-2" />
                  إنشاء قالب جديد
                </Button>
              )}
            </CardContent>
          </Card>
        ) : (
          <div className={viewMode === "grid" ? 
            "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6" : 
            "space-y-4"
          }>
            {filteredTemplates.map((template) => (
              <Card
                key={template.id}
                className="bg-slate-800/50 border-slate-700/50 backdrop-blur-sm hover:bg-slate-700/30 transition-all duration-200"
              >
                <CardHeader>
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <CardTitle className="text-white text-lg mb-2 flex items-center gap-2">
                        {template.name}
                        {template.isDefault && (
                          <Badge variant="secondary" className="text-xs">
                            افتراضي
                          </Badge>
                        )}
                      </CardTitle>
                      <p className="text-slate-400 text-sm line-clamp-2">
                        {template.description}
                      </p>
                    </div>
                    <Button
                      variant="ghost"
                      size="sm"
                      className="text-slate-400 hover:text-white"
                    >
                      <MoreVertical className="h-4 w-4" />
                    </Button>
                  </div>
                </CardHeader>
                
                <CardContent className="space-y-4">
                  <div className="flex items-center justify-between text-sm">
                    <Badge variant="outline" className="border-slate-600 text-slate-300">
                      {template.category === "gaming" ? "ألعاب" :
                       template.category === "digital" ? "رقمية" :
                       template.category === "subscriptions" ? "اشتراكات" :
                       template.category}
                    </Badge>
                    <span className="text-slate-400">
                      {template.fields.length} حقل
                    </span>
                  </div>
                  
                  <div className="text-xs text-slate-500">
                    آخر تحديث: {template.updatedAt.toLocaleDateString('ar-SA')}
                  </div>
                  
                  <div className="flex gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleEditTemplate(template)}
                      className="flex-1 border-slate-600 text-slate-300 hover:bg-slate-700"
                    >
                      <Edit className="h-4 w-4 ml-2" />
                      تعديل
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handlePreviewTemplate(template)}
                      className="border-slate-600 text-slate-300 hover:bg-slate-700"
                    >
                      <Eye className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleDuplicateTemplate(template)}
                      className="border-slate-600 text-slate-300 hover:bg-slate-700"
                    >
                      <Copy className="h-4 w-4" />
                    </Button>
                    {!template.isDefault && (
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleDeleteTemplate(template.id)}
                        className="border-red-600 text-red-400 hover:bg-red-500/10"
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    )}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        )}

        {/* Create Template Modal */}
        {showCreateForm && (
          <ProductForm
            onSave={handleSaveTemplate}
            onPreview={handlePreviewTemplate}
            onClose={handleCloseForm}
            isLoading={false}
          />
        )}

        {/* Edit Template Modal */}
        {showEditForm && editingTemplate && (
          <ProductForm
            template={editingTemplate}
            onSave={handleSaveTemplate}
            onPreview={handlePreviewTemplate}
            onClose={handleCloseForm}
            isLoading={false}
          />
        )}
      </div>
    </div>
  )
}
